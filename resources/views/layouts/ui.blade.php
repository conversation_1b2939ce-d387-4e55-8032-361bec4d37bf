@extends('frontend.default')

@section('title', 'Demo')

@section('css')
    <link rel="stylesheet" href="{{ assetMix('css/buttons.css') }}" type="text/css">
    <link rel="stylesheet" href="{{ assetMix('css/booking.css') }}" type="text/css">
@endsection
@section('appScript')
    <script src="{{ asset('theme/ag/vendor/bootstrap-datepicker/dist/js/bootstrap-datepicker.min.js') }}"></script>
@endsection

@section('pageNameFrontend')
    <div class="text-name">Demo</div>
@endsection

@section('content') 
<div class="container">
    <div class="col-lg-12  form-group">
        Font : https://fontawesome.com/v4.7/icons/
    </div>
    <div class="btn-group">
        <p><a class="btn active-primary">Active</a></p>
        <p><a href="#" class="btn btn-ct-primary" role="button">出勤登録</a></p>
        <p><a href="#" class="btn btn-ct-primary disabled" role="button">出勤編集</a></p>
    </div>
    
    <div class="col-lg-12  form-group">
        <button type="button" class="btn btn-default">Default</button>
        <button type="button" class="btn btn-primary">Primary</button>
        <button type="button" class="btn btn-secondary">Secondary</button>
        <button type="button" class="btn btn-info">Info</button>
        <button type="button" class="btn btn-success">Success</button>
        <button type="button" class="btn btn-danger">Danger</button>
        <button type="button" class="btn btn-warning">Warning</button>
    </div>
    <div class="col-lg-12 form-group">
        <button type="button" class="btn btn-sm btn-default">Default</button>
        <button type="button" class="btn btn-sm btn-primary">Primary</button>
        <button type="button" class="btn btn-sm btn-secondary">Secondary</button>
        <button type="button" class="btn btn-sm btn-info">Info</button>
        <button type="button" class="btn btn-sm btn-success">Success</button>
        <button type="button" class="btn btn-sm btn-sm btn-danger">Danger</button>
        <button type="button" class="btn btn-sm btn-warning">Warning</button>
    </div>
    <div class="col-lg-12 form-group">
        <div class="form-group">
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text"><i class="ni ni-calendar-grid-58"></i></span>
                </div>
                <input class="form-control datepicker" placeholder="Select date" type="text" value="06/20/2020">
            </div>
        </div>
    </div>
    <div class="col-lg-12 form-group">
        <div class="form-group">
            <div class="input-group">
                <div class="input-group-prepend">
                    <span class="input-group-text" id="basic-addon1">@</span>
                </div>
                <input type="text" class="form-control" placeholder="Username" aria-label="Username" aria-describedby="basic-addon1">
            </div>
        </div>
        
        <div class="form-group">
            <div class="input-group">
              <input type="text" class="form-control" placeholder="Recipient's username" aria-label="Recipient's username" aria-describedby="basic-addon2">
              <div class="input-group-append">
                <span class="input-group-text" id="basic-addon2">@example.com</span>
              </div>
            </div>
        </div>
        
        <div class="form-group">
            <label class="form-control-label" for="basic-url">Your vanity URL</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text" id="basic-addon3">https://example.com/users/</span>
              </div>
              <input type="text" class="form-control" id="basic-url" aria-describedby="basic-addon3">
            </div>
        </div>
        
        <div class="form-group">
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text">$</span>
              </div>
              <input type="text" class="form-control" aria-label="Amount (to the nearest dollar)">
              <div class="input-group-append">
                <span class="input-group-text">.00</span>
              </div>
            </div>
        </div>
        
        <div class="form-group">
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text">With textarea</span>
              </div>
              <textarea class="form-control" aria-label="With textarea"></textarea>
            </div>
        </div>
    </div>
</div>
@endsection
