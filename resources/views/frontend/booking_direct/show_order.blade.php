@inject('mCoupon', 'App\Models\Coupon')
@inject('cstOrder', 'App\Constants\Orders' )
@inject('orderService', 'App\Services\OrderService')
@php
use Carbon\Carbon;
@endphp
@extends('frontend.default')

@section('title', '予約内容確認')

@section('css')
    <link rel="stylesheet" href="{{ assetMix('css/confirm.css') }}" type="text/css">
    <link rel="stylesheet" href="{{ assetMix('css/booking.breadcrumb.css') }}" type="text/css">
    <link rel="stylesheet" href="{{ assetMix('css/booking.direct.css') }}" type="text/css">
    <link rel="stylesheet" href="{{ assetMix('css/modal.coupon.option.css') }}" type="text/css">
@endsection

@section('pageNameFrontend')
    <div class="text-name">予約内容確認 </div>
@endsection

@section('content')
    <div class="confirm-page" id="page-booking-pay-confirm">
        <div class="container">
            <form method="POST" action="{{ route('booking.update.order') }}" id="page-show-orders">
                @csrf
                <div class="head">
                    <div class="text">
                        <p>Q.下記予約内容でお問違いございませんか？</p>
                    </div>

                    @if (isset($dataOrders) && count($dataOrders) == 1)
                        @foreach ($dataOrders as $orders)
                            <div class="label">
                                <button type="button" class="member {{ $orders->customer_code ? 'selected' : '' }}"
                                    id="click_member" disabled>会員</button>
                                <button type="button" class="member {{ !$orders->customer_code ? 'selected' : '' }}"
                                    id="click_member2" disabled>非会員</button>
                            </div>
                        @endforeach
                    @endif
                </div>
                @forelse ($dataOrders as $orders)
                    @php
                        $orderCouponNormal = null;
                        $orderCouponTicker = null;
                        $orderCouponMember = null;
                        $customer = $dataCustomer[$orders->id];
                        $totalPrice = $orders->type_pay == $cstOrder::TYPE_PAYMENT_CARD ? $orders->total_price / (1 + $orderService::PERCENT / 100) : $orders->total_price;
                    @endphp
                    <div class="row information {{ count($dataOrders) == 1 ? '' : ' bg-information' }} order-item item_{{ $orders->id }}"
                        attr-order-id={{ $orders->id }}>
                        <div class="col-sm-6 col-left">
                            <img src="{{ asset('images/background-confirm-left.png') }}" class="background">
                            <section class="spikes">
                                <div class="hidden">
                                    <p class="price-course">{{ $orders->course_price + $orders->price_fee_new }}</p>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox"
                                        class="form-check-input {{ count($dataOrders) == 1 ? 'hidden' : '' }}"
                                        name="input_check[]" {{ count($dataOrders) == 1 ? 'checked' : '' }}
                                        value="{{ $orders->id }}">
                                </div>
                                <div class="text-head">
                                    予約内容
                                </div>
                                <div class="box-infor">
                                    <div class="text-row">
                                        <input type="hidden" name="date_time" value="0">
                                        <div class="text">予約日</div>
                                        <div class="info">{{ formatDateJp($orders->date) }}</div>
                                    </div>
                                    <div class="text-row">
                                        <div class="text">予約時間</div>
                                        @php
                                            $dateStart = Carbon::parse($orders->date_start);
                                            $dateEnd = Carbon::parse($orders->date_end);
                                            $date = Carbon::parse($orders->date);
                                            $timeStart = sprintf('%02d', $dateStart->hour) . ':' . sprintf('%02d', $dateStart->minute);
                                            if ($dateStart->day != $date->day) {
                                                $timeStart = sprintf('%02d', $dateStart->hour + 24) . ':' . sprintf('%02d', $dateStart->minute);
                                            }
                                            $timeEnd = sprintf('%02d', $dateEnd->hour) . ':' . sprintf('%02d', $dateEnd->minute);
                                            if ($dateEnd->day != $date->day) {
                                                $timeEnd = sprintf('%02d', $dateEnd->hour + 24) . ':' . sprintf('%02d', $dateEnd->minute);
                                            }
                                        @endphp
                                        <div class="info">{{ $timeStart }} ~
                                            {{ $timeEnd }}</div>
                                    </div>
                                    <div class="text-row">
                                        <div class="text">予約方法</div>
                                        <div class="info">{{ $orders->textMethod }}</div>
                                    </div>
                                    <div class="text-row">
                                        <div class="text">キャスト名</div>
                                        <div class="info">{{ $orders->cast_name }}</div>
                                    </div>
                                    <div class="text-row">
                                        <div class="text">コース</div>
                                        <div class="info">{{ $orders->course_time }}分</div>
                                    </div>
                                    <div class="text-row">
                                        <div class="text">スタート +</div>
                                        <div class="info">{{ $orders->is_minute_add ? '有り' : '無し' }}</div>
                                    </div>
                                    <div class="text-row">
                                        <div class="text">キャストとのお遊び経験</div>
                                        <div class="info">{{ $orders->is_first ? '無し' : '有り' }}</div>
                                    </div>
                                    <div class="text-row">
                                        <div class="text">オプション</div>
                                        <div class="info col-option">
                                            @php
                                                $optionParent = null;
                                                $optionChild = null;
                                                if (count($orders->optionParent)) {
                                                    $optionParent = $orders->optionParent[0];
                                                }
                                                if (count($orders->optionChild)) {
                                                    $optionChild = $orders->optionChild[0];
                                                }
                                            @endphp

                                            @if ($optionParent)
                                                <span col-option_parent_{{ $orders->id }}>
                                                    {{ $optionParent->pivot->name }}</span>
                                            @else
                                                <span class='col-null-option'>無し</span>
                                                <span col-option_parent_{{ $orders->id }}></span>
                                            @endif

                                            @if ($optionChild)
                                                <span
                                                    col-option_child_{{ $orders->id }}>({{ $optionChild->pivot->name }})</span>
                                            @else
                                                <span col-option_child_{{ $orders->id }}></span>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="text-row text-coupon">
                                        <div class="text">割引</div>
                                        <div class="info col-coupon" style="text-align: right;">
                                            @if (count($orders->coupons))
                                                @foreach ($orders->coupons as $item)
                                                    @php
                                                        $strTypeCoupon = '';
                                                        if ($item->type == $mCoupon::TYPE_COUPON) {
                                                            $orderCouponNormal = $item;
                                                            $strTypeCoupon = 'coupon_normal';
                                                        }
                                                        if ($item->type == $mCoupon::TYPE_COUPON_MEMBER) {
                                                            $orderCouponMember = $item;
                                                            $strTypeCoupon = 'coupon_member';
                                                        }
                                                        if ($item->type == $mCoupon::TYPE_COUPON_TICKET) {
                                                            $orderCouponTicker = $item;
                                                            $strTypeCoupon = 'coupon_ticket';
                                                        }
                                                    @endphp
                                                    <div class="col-{{ $strTypeCoupon }}_{{ $orders->id }}">
                                                        {{ $item->pivot->name }}
                                                    </div>
                                                @endforeach
                                            @else <div class='col-null-coupon'>無し</div>
                                            @endif
                                        </div>
                                    </div>
                                    @php
                                        $totalPriceNotPoint = $orders->price_point ? $totalPrice + round($orders->point / $orders->price_point) : $totalPrice;
                                    @endphp
                                    <div class="text-row-2">
                                        <div class="text">総額（オプション + 指名料 + 税）</div>
                                        <div class="info">
                                            <span
                                                class="total-price-order">{{ number_format($totalPriceNotPoint) }}</span>
                                            円
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>
                        <div class="col-sm-6">
                            {{-- <img src="{{ asset('images/layer-confirm-right.png') }}" class="layer-left"> --}}
                            <section class="spikes-2">
                                <div class="text-head">
                                    会員情報
                                </div>
                                <div class="hidden info-customer-hidden">
                                    <p class="phone">{{ formatPhone($orders->phone) }}</p>
                                    <p class="name">{{ $orders->name }}</p>
                                    <p class="point">{{ $customer ? $customer->point : 0 }}</p>
                                </div>
                                <div class="box-infor">
                                    <div class="text-row">
                                        <div class="text">会員番号</div>
                                        <div class="info customer-code">
                                            {{ $customer ? $customer->customer_code : '-' }}
                                        </div>
                                    </div>
                                    <div class="text-row">
                                        <div class="text">お客様名</div>
                                        <div class="info customer-name">{{ $orders->name }}</div>
                                    </div>
                                    <div class="text-row">
                                        <div class="text">電話番号</div>
                                        <div class="info customer-phone">{{ formatPhone($orders->phone) }}</div>
                                    </div>
                                    <div class="text-row">
                                        <div class="text">生年月日</div>
                                        <div class="info customer-birthday">
                                            {{ $customer ? formatDateJp($customer->birthday) : '-' }}
                                        </div>
                                    </div>
                                    <div class="text-row-2">
                                        <div class="text">所持ポイント</div>
                                        <div class="info customer-point">
                                            {{ $customer ? number_format($customer->point) : '-' }}
                                            pt
                                        </div>
                                    </div>
                                </div>
                            </section>
                            <section class="spikes-3">
                                <div class="text-row">
                                    <div class="text col-sm-6">ポイントを使用</div>
                                    <div class="text col-sm-3">
                                        <input type="hidden" name="point_use_{{ $orders->id }}"
                                            value="{{ $customer ? $orders->point : 0 }}">
                                        <div class="price">
                                            <span
                                                class="point-use">{{ $customer && $setting->point_price ? '- ' . round($orders->point / $setting->point_price) : '-' }}</span>
                                            円
                                        </div>
                                    </div>
                                    <div class="info col-sm-3">
                                        <div class="price">
                                            <span class="total-price-after-point-used">
                                                {{ $customer ? number_format($totalPrice) : '-' }}
                                            </span>
                                            円
                                        </div>
                                    </div>
                                </div>
                                <div class="text-row-2">
                                    <div class="text col-sm-6">ポイントを貯める</div>
                                    <div class="text col-sm-3" id="point-plus">
                                        <span
                                            class="point-price-order">{{ $customer && $setting->price_point ? '+ ' . round($totalPrice / $setting->price_point) : '-' }}</span>
                                        pt
                                    </div>
                                    <div class="info col-sm-3">
                                        @php
                                            $pointPlus = $setting->price_point ? round($totalPrice / $setting->price_point) : 0;
                                        @endphp
                                        <span
                                            class="total-point-customer">{{ $customer ? $pointPlus + $customer->point - $orders->point : '-' }}</span>
                                        pt
                                    </div>
                                </div>
                                <div class="text-row-3">
                                    <button type="button" class="btn btn-show-my-modal" data-toggle="modal"
                                        data-target="#myModal{{ $orders->id }}" {{ $customer ? '' : 'disabled' }}
                                        {{ $orders->customer_code ? '' : 'disabled' }}>使う</button>
                                </div>
                            </section>
                            @if (count($dataOrders) != 1)
                                <div class="form-inline group-button">
                                    <button type="button" class="btn btn-warning btn-modal-orange-yellow"
                                        data-toggle="modal" data-target="#couponModal{{ $orders->id }}">
                                        その他
                                    </button>
                                    <button type="button" class="btn btn-warning btn-modal-orange-yellow"
                                        data-toggle="modal" data-target="#optionModal{{ $orders->id }}">
                                        オプション
                                    </button>
                                    <div class="input-group">
                                        <input type="text" class="form-control input-search-phone"
                                            placeholder="Search phone" name="phone_number_{{ $orders->id }}"
                                            data-type='phone' maxlength="12" value="{{ $orders->phone_point }}">
                                        <div class="input-group-append btn-search">
                                            <button class="btn btn-secondary btn-search-member" type="button">
                                                <span class="icon-search"><i class="fa fa-search"></i></span>
                                                <span class="icon-spin hidden"><i class="fa fa-refresh fa-spin"></i></span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <div class="modal js-default-popup modal-input-point-hidden" id="myModal{{ $orders->id }}"
                            attr-order-id="{{ $orders->id }}" attr-order-phone-point="{{ $orders->phone_point }}">
                            <div class="modal-dialog modal-lg">
                                <div class="container">
                                    <div class="modal-content">
                                        <div class="hidden">
                                            <p class="total-point">
                                                {{ $customer ? $customer->point : 0 }}</p>
                                            <p class="total-price-order-no-point">
                                                {{ $totalPriceNotPoint }}
                                            </p>
                                            </p>
                                            <p class="phone-point">
                                                {{ $orders->phone_point }}</p>
                                            </p>
                                        </div>
                                        <div class="modal-header">
                                            <div class="content-popup">ポイント使用画面です</div>
                                        </div>
                                        <div class="clearfix">
                                            <div class="text-box">
                                                <div class="text">所持ポイント&emsp;&emsp;</div>
                                                <div class="point">
                                                    {{ $customer ? number_format($customer->point) : 0 }}
                                                    pt</div>
                                            </div>
                                            <div class="text-box">使用するポイント数を入力してください</div>
                                            <input class="input-point point_use_modal form-control" type="text"
                                                data-type="currency" maxlength="9" min='0' attr-phone
                                                value="{{ $orders->point }}"
                                                oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                                            <div class="alert alert-danger"></div>
                                            <div class="text-box-2">
                                                <div class="text">ポイント使用後の料金&emsp;&emsp;</div>
                                                <div class="price"> <span class="modal-price-after-point-use">
                                                        {{ number_format($totalPrice) }}
                                                    </span>
                                                    円
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-primary btn-close-modal">OK</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="input-form">
                            <input type="hidden" name="option_parent_{{ $orders->id }}"
                                value="{{ $optionParent ? $optionParent->pivot->option_id : 0 }}">
                            <input type="hidden" name="option_child_{{ $orders->id }}"
                                value="{{ $optionChild ? $optionChild->pivot->option_id : 0 }}">
                            <input type="hidden" name="coupon_normal_{{ $orders->id }}"
                                value="{{ $orderCouponNormal ? $orderCouponNormal->pivot->coupon_id : null }}">
                            <input type="hidden" name="coupon_member_{{ $orders->id }}"
                                value="{{ $orderCouponMember ? $orderCouponMember->pivot->coupon_id : null }}">
                            <input type="hidden" name="coupon_ticket_{{ $orders->id }}"
                                value="{{ $orderCouponTicker ? $orderCouponTicker->pivot->coupon_id : null }}">
                            <div class="tax-percentage-{{ $orders->id }} hidden">{{ $orders->tax_percentage }}</div>
                        </div>
                    </div>

                    @include('frontend.booking_direct.modal_coupon', [
                    'couponMember' => $dataCouponMember[$orders->id],
                    'couponNormals' => $dataCouponNormals[$orders->id],
                    'couponTickets' => $dataCouponTickets[$orders->id],
                    'orderCouponMember' => $orderCouponMember,
                    'orderCouponNormal' => $orderCouponNormal,
                    'orderCouponTicker' => $orderCouponTicker,
                    'orders' => $orders,
                    'customer' => $customer,
                    ])

                    @include('frontend.booking_direct.modal_option', [
                    'listOptions' => $dataOptions[$orders->id],
                    ])
                @empty
                @endforelse

                <div class="row button-confirm">
                    <button type="submit" class="btn btn-confirm">決定</button>
                </div>
                <br>
                <br><br><br>
            </form>

            @include('frontend.booking.steps', [
            'active'=> 3,
            'buttonNext' => false,
            'dataOrders' => $dataOrders,
            ])
        </div>
    </div>

@endsection

@section('appScript')
    <script>
        var pointPrice = @json($setting->point_price);
        var pricePoint = @json($setting->price_point);
        var taxPercentage = @json($setting->tax_percentage);
    </script>
    <script src="{{ assetMix('js/page_cast.js') }} "></script>
    <script src="{{ assetMix('js/page_booking.js') }} "></script>
@endsection
