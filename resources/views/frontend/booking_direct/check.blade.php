@extends('frontend.default')

@section('title', '店頭受付トップ')

@section('css')
    <link rel="stylesheet" href="{{ assetMix('css/booking.direct.css') }}" type="text/css">
    <link rel="stylesheet" href="{{ assetMix('css/booking.breadcrumb.css') }}" type="text/css">
@endsection

@section('pageNameFrontend')
    <div class="text-name">店頭受付トップ</div>
@endsection

@section('content')
    <div class="container">
        <div id="booking-pay-check">
            <div class="group-title">
                <p class="purple">本日は、ご来店頂きまして、誠にありがとうございます</p>
                <p class="blue">ご予約時の電話番号を入力してください</p>
            </div>
            <div class="group-button">
                <a href="{{ route('booking.phone') }}" class="btn btn-pink" role="button">予約している</a>
                <a href="{{ route('booking.direct.cast') }}" class="btn btn-success" role="button">予約していない</a>
            </div>
            @include('frontend.booking.steps', [
            'active'=> 4,
            'buttonNext' => false
            ])
        </div>
    </div>
@endsection

@section('scripts')
    <script src="{{ assetMix('js/page_cast.js') }} "></script>
@endsection
