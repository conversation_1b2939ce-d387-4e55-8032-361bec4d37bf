@inject('mOrder', 'App\Models\Order')
@inject('constBooking', 'App\Constants\Booking')
@php
use Carbon\Carbon;
@endphp

@extends('frontend.default')

@section('title', '店頭受付トップ')

@section('css')
    <link rel="stylesheet" href="{{ assetMix('css/select.cast.css') }}" type="text/css">
    <link rel="stylesheet" href="{{ assetMix('css/select.course.css') }}" type="text/css">
    <link rel="stylesheet" href="{{ assetMix('css/booking.breadcrumb.css') }}" type="text/css">
    <link rel="stylesheet" href="{{ assetMix('css/booking.direct.css') }}" type="text/css">
@endsection
@section('pageNameFrontend')
    <div class="text-name">店頭受付トップ</div>
@endsection

@php
$courseIds = null;
$messageError = '';
if (Session('error')) {
    $messageError = session('error');
}
@endphp

@section('content')
    <div class="select-cast-page select-course-page" id="booking-cast-direct">
        <div class="container">
            <form action="{{ route('booking.direct.cast') }}" method="GET" id="form-search-cast">
                <input type="hidden" name="cast_type" value="{{ (int) app('request')->input('cast_type') }}" />
                <input type="hidden" name="name_cast" value="{{ app('request')->input('name_cast') }}">
            </form>
            <form action="{{ route('booking.store.cast.course') }}" method="POST" id="form-cast">
                @csrf
                <input type="hidden" name="date" value="{{ $dateNow }}">
                <input type="hidden" name="cast_id" value="{{ isset($orders) ? $orders->cast_id : 0 }}">
                <input type="radio" name="add_time" value="1" class="hidden"
                    {{ isset($orders) && $orders->add_time == 1 ? 'checked' : '' }}>
                <div class="select-cast-title-page">
                    <div class=" hidden">
                        <input type="checkbox" name="method" value="{{ $mOrder::METHOD_DIRECT_SHOP }}"
                            {{ isset($orders) && $orders->method == $mOrder::METHOD_DIRECT_SHOP ? 'checked' : '' }}
                            checked>
                        <label class="cast-direct-reservation"> キャスト直予約</label>
                    </div>
                </div>
                <div class="filter">
                    <div class="button-filter">
                        <button type="button" data-type="1"
                            class="filter-cast-new {{ app('request')->input('cast_type') == 1 ? 'selected' : '' }}">新人</button>
                    </div>
                    <div class="text-1 select-cast-text-hint">
                        <p>Q. キャストをお選びください</p>
                    </div>
                    <div class="form-search">
                        <div class="search-list-cast">
                            <input type="text" name="name_cast" id="name_cast" class="form-control" maxlength="15"
                                oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);"
                                placeholder="登録されているキャスト名で検索" value="{{ app('request')->input('name_cast') }}">
                            <button type="button" class="btn button-search">検索</button>
                        </div>
                        <div id="listCast"></div>
                    </div>
                </div>

                <div class="row cast-items">
                    @if (!count($casts) && (app('request')->input('cast_type') || app('request')->input('name_cast')))
                        <h3 class="middle-text text-center color-red">※該当なデータ無し</h3>
                    @elseif(!count($casts))
                        <h3 class="middle-text text-center color-red">※出勤しているキャストがいません</h3>
                    @else
                        @foreach ($casts as $cast)
                            <div class="cast-item-el">
                                <div class="hashtag">
                                    @if ($cast->new)
                                        <button type="button" class="hashtag-1">新人</button>
                                    @endif
                                    @if (!$cast->matto_play)
                                        <button type="button" class="hashtag-2">マット無し</button>
                                    @endif
                                    @if ($cast->is_foreigner)
                                        <button type="button" class="hashtag-3">外国人 OK</button>
                                    @endif
                                </div>
                                <div class="cast-item-1 {{ isset($orders) && $orders->cast_id == $cast->id ? 'active' : '' }}"
                                    attr-id="{{ $cast->id }}">
                                    <?php $image = $cast->images()->first(); ?>
                                    <div class="image-cast">
                                        <a class="preview-cast-image-btn" rel="fancybox-button" href="#">
                                            <img src="{{ asset($image->path) }}" class="img-cast">
                                            @foreach ($cast->images as $it)
                                                <span data-src="{{ asset($it->path) }}"></span>
                                            @endforeach
                                        </a>
                                    </div>
                                    <div class="information-cast">
                                        <div class="cast-short-info">
                                            <p> <span class="name">{{ $cast->name_cast }}</span>
                                                <span>({{ $cast->age }}歳)</span>
                                            </p>
                                            <p> T : {{ $cast->height }} cm</p>
                                        </div>
                                        <div class="body-measurements">
                                            <p>B : {{ $cast->chest }}cm ({{ $cast->cup }} cup)</p>
                                            <p>W : {{ $cast->waist }}cm </p>
                                            <p>H : {{ $cast->hips }}cm</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="box-course">
                                    @if ($cast->online)
                                        <div class="text-online">即案内可能</div>
                                        <button class="btn btn-outline-success btn-show-course" type="button"
                                            data-toggle="collapse" data-target="#collapseOpion{{ $cast->id }}"
                                            aria-expanded="false" attr-cast-id="{{ $cast->id }}"
                                            aria-controls="collapseOpion{{ $cast->id }}">
                                            <div class="icon-add">
                                                コース選択 <span class="spin hidden"><i class="fa fa-refresh fa-spin"
                                                        aria-hidden="true"></i></span>
                                            </div>
                                        </button>
                                    @elseif ($cast->status_book == $constBooking::WORKING)
                                        @php
                                            $dateCalendar = Carbon::parse($dateNow);
                                            $hourStart = $cast->timeBook->hour;
                                            if ($cast->timeBook->day != $dateCalendar->day) {
                                                $hourStart = $cast->timeBook->hour + 24;
                                            }
                                            $timeString = sprintf('%02d', $hourStart) . ':' . sprintf('%02d', $cast->timeBook->minute);
                                        @endphp
                                        <div class="text-online">
                                            次回、{{ sprintf('%02d', $hourStart) }}:{{ sprintf('%02d', $cast->timeBook->minute) }}～
                                            案内可能
                                        </div>
                                        <form></form>
                                        <form method="POST" action="{{ route('booking.store-calendar') }}">
                                            @csrf
                                            <input type="hidden" name="castId" value="{{ $cast->id }}">
                                            <input type="hidden" name="date" value="{{ $dateNow }}">
                                            <input type="hidden" name="hour" value="{{ $hourStart }}">
                                            <input type="hidden" name="minute" value="{{ $cast->timeBook->minute }}">
                                            <input type="hidden" name="method_booking"
                                                value="{{ $mOrder::METHOD_DIRECT_SHOP }}">
                                            <input type="hidden" name="free_booking" value="0">
                                            <input type="hidden" name="changeBooking" value="1">
                                            <button class="btn btn-outline-success change-booking" type="submit">
                                                <div class="icon-add">予約する</div>
                                            </button>
                                        </form>
                                    @elseif ($cast->status_book == $constBooking::NO_WORK)
                                        @php
                                            $dateCalendar = Carbon::parse($cast->calendars->date);
                                            $dateStartCalendar = Carbon::parse($cast->calendars->date_start);
                                            $hourStart = $dateStartCalendar->hour;
                                            if ($dateStartCalendar->day != $dateCalendar->day) {
                                                $hourStart = $dateStartCalendar->hour + 24;
                                            }
                                            $timeString = sprintf('%02d', $hourStart) . ':' . sprintf('%02d', $dateStartCalendar->minute);
                                        @endphp
                                        <div class="text-online">次回、 {{ $timeString }} ~ 案内可能
                                        </div>
                                        <form></form>
                                        <form method="POST" action="{{ route('booking.store-calendar') }}">
                                            @csrf
                                            <input type="hidden" name="castId" value="{{ $cast->id }}">
                                            <input type="hidden" name="date" value="{{ $dateNow }}">
                                            <input type="hidden" name="hour" value="{{ $hourStart }}">
                                            <input type="hidden" name="minute" value="{{ $dateStartCalendar->minute }}">
                                            <input type="hidden" name="method_booking"
                                                value="{{ $mOrder::METHOD_DIRECT_SHOP }}">
                                            <input type="hidden" name="free_booking" value="0">
                                            <input type="hidden" name="changeBooking" value="1">
                                            <button class="btn btn-outline-success change-booking" type="submit">
                                                <div class="icon-add">予約する</div>
                                            </button>
                                        </form>
                                    @elseif ($cast->status_book == $constBooking::END_WORK)
                                        @if ($cast->calendarNext)
                                            @php
                                                $dateCalendar = Carbon::parse($cast->calendarNext->date);
                                                $dateStartCalendar = $cast->timeBook;
                                                $hourStart = $dateStartCalendar->hour;
                                                if ($dateStartCalendar->day != $dateCalendar->day) {
                                                    $hourStart = $dateStartCalendar->hour + 24;
                                                }
                                                $timeString = sprintf('%02d', $hourStart) . ':' . sprintf('%02d', $dateStartCalendar->minute);
                                            @endphp
                                            <div class="text-online">
                                                次回：{{ $dateCalendar->month . ' 月 ' . $dateCalendar->day . ' 日 ' . $timeString }}～予約可能
                                            </div>
                                            <form></form>
                                            <form method="POST" action="{{ route('booking.store-calendar') }}">
                                                @csrf
                                                <input type="hidden" name="castId" value="{{ $cast->id }}">
                                                <input type="hidden" name="date" value="{{ $cast->calendarNext->date }}">
                                                <input type="hidden" name="hour" value="{{ $hourStart }}">
                                                <input type="hidden" name="minute"
                                                    value="{{ $dateStartCalendar->minute }}">
                                                <input type="hidden" name="method_booking"
                                                    value="{{ $mOrder::METHOD_DIRECT_SHOP }}">
                                                <input type="hidden" name="free_booking" value="0">
                                                <input type="hidden" name="changeBooking" value="1">
                                                <button class="btn btn-outline-success change-booking" type="submit">
                                                    <div class="icon-add">予約する</div>
                                                </button>
                                            </form>
                                        @else
                                            <div class="text-online">次回：お問い合わせください
                                            </div>
                                        @endif
                                    @else
                                        <div class="text-online">次回：完売</div>
                                    @endif
                                    <div class="collapse" id="collapseOpion{{ $cast->id }}">
                                        <div class="list-option">
                                            @if ($listCourse)
                                                <div class="row option">
                                                    <div class="col-md-12">
                                                        @foreach ($listCourse as $key => $course)
                                                            <button type="button" class="btn btn-course select-course"
                                                                @php
                                                                    $strStyle = 'border: 2px solid' . $course->color;
                                                                    if (isset($orders) && $orders->cast_id == $cast->id && $orders->course_id == $course->id) {
                                                                        $strStyle = 'border: 2px solid' . $course->color . '; background: ' . $course->color;
                                                                        $courseIds = $course->id;
                                                                    }
                                                                @endphp style="{{ $strStyle }}"
                                                                id="{{ $cast->id }}-{{ $course->id }}"
                                                                attr-color='{{ $course->color }}' attr-check='0'
                                                                attr-time="{{ $course->time }}">
                                                                <div class="row">
                                                                    <div class="col-sm-5">
                                                                        {{ $course->name }}
                                                                    </div>
                                                                    <div class="col-sm-3">
                                                                        {{ $course->time }} 分
                                                                    </div>
                                                                    <div class="col-sm-4">
                                                                        <div class="price-text">
                                                                            <div class="price">
                                                                                {{ $course->price }} 円
                                                                            </div>
                                                                            <div class="text">
                                                                                {{ $course->tax_included ? '' : '※税抜金額' }}
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </button>
                                                        @endforeach
                                                        <div class="col-sm-12">
                                                            <div class="add-time">
                                                                <input type="checkbox" class="add-time-checkbox"
                                                                    cast-item-el={{ $cast->id }} name="add_time"
                                                                    {{ $courseIds && $orders->add_time == 1 ? 'checked' : '' }}>
                                                                <label class="add-time-lable"> スタートプラスする</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" name="course_id" value="{{ $courseIds }}">
                            </div>
                        @endforeach
                    @endif
                </div>
                <div class="checkbox-free">
                    <input type="checkbox" id="free" name="free" value="1">
                    <label class="telephone"> フリー</label>
                    @include('frontend.booking.steps', [
                    'active'=> 2,
                    'buttonNext' =>
                    true ])
                </div>
            </form>
        </div>
    </div>
@endsection

@section('appScript')
    <script>
        var messageError = @json($messageError);
    </script>
    <script src="{{ assetMix('js/page_cast.js') }} "></script>
    <script src="{{ assetMix('js/page_booking_cast.js') }} "></script>
    <script src="{{ assetMix('js/page_booking_quick_view.js') }}"></script>
@endsection
