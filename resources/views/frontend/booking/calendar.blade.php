@extends('frontend.default')

@section('css')
    <link rel="stylesheet" href="{{ assetMix('css/calendar.css') }}" type="text/css">
    <link rel="stylesheet" href="{{ assetMix('css/booking.breadcrumb.css') }}" type="text/css">
@endsection

@section('title', '予約/受付トップ ')

@section('pageNameFrontend')
    <div class="text-name">カレンダートップ</div>
@endsection

@section('content')
    <div class="calendar-page">
        <div class="container">
            <div class="text">
                <p>Q. 予約日をお選びください。</p>
            </div>
            <div id="datepicker"> </div>
            <form action="{{ route('booking.store-calendar') }}" method="POST" class="calendar">
                @csrf
                <input type="hidden" name="date" value="">
                @include('frontend.booking.steps', [
                'active'=> 2,
                'buttonNext' => true
                ])
            </form>
        </div>
    </div>
    </div>
@endsection

@section('appScript')
    <script src="{{ assetMix('js/page_cast.js') }} "></script>
    <script src="{{ asset('js/page_booking_calendar.js') }}"></script>
@endsection
