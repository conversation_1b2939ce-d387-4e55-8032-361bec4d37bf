@extends('admin.layouts.app')

@section('css')
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <link rel="stylesheet" href="{{ assetMix('css/cast.schedule.css') }}" type="text/css">
@endsection

@section('title', '予約管理')

@section('adminPageName')
    <h2 class="page-name-text">予約管理</h2>
@endsection

@section('pageNameBreadcrumb')
    <div class="breadcrumb">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.index') }}">管理トップページ</a></li>
                <li class="breadcrumb-item active" aria-current="page">予約管理</li>
            </ol>
        </nav>
    </div>
@endsection

@section('content')
    <div class="container main">
        <div class="row name-page">
            <p>Q. キャストの予約状況です。</p>
        </div>
        <div class="infor">
            <div class="name-cast">キャスト名: @if (isset($castCalendar))
                    {{ $castCalendar->name_cast }} @endif &nbsp; さん
            </div>
            <div class="date-time row">
                <span>予約日:</span>
                <div id="time-select"></div>
                <form action="{{ route('admin.booking.cast.schedule', $id) }}" method="GET" id="select-date">
                    <img src="{{ asset('images/icons/calender_clock.png') }}" alt="calender clock"
                        class="datepicker img-datepicker">
                    <input type="date" name="date" class="hidden" value="{{ $dateSelect->format('Y-m-d') }}">
                </form>
            </div>
        </div>
        @if (!isset($castCalendar))
            <p class="text-center">※キャストの出勤が登録されていません。</p>
        @else
            <div class="table-schedule row">
                @php
                    $index = 0;
                @endphp
                @for ($i = 1; $i <= $numberTable; $i++)
                    <table class="schedule-detail table table-bordered table-responsive ">
                        @for ($j = 1; $j <= 60 / $timeAdd; $j++)
                            <tr>
                                <td class="time">
                                    @if ($i == 1 && $j == 1)
                                        @php
                                            if ($timeTableBegin->lessthan($dateStart)) {
                                                $timeStart = clone $timeTableBegin;
                                            } else {
                                                $timeStart = clone $dateStart;
                                            }
                                            $hourStart = $timeStart->hour;
                                            if ($timeStart->day != $dateSelect->day) {
                                                $hourStart += 24;
                                            }
                                            $timeStartString = sprintf('%02d', $hourStart) . ':' . sprintf('%02d', $timeStart->minute);
                                        @endphp
                                        {{ $timeStartString }}
                                    @else
                                        @php
                                            if ($timeTableBegin->lessthan($dateStart)) {
                                                $timeStart = clone $timeTableBegin->addMinutes($timeAdd);
                                            } else {
                                                $timeStart = clone $dateStart->addMinutes($timeAdd);
                                                $timeTableBegin->addMinutes($timeAdd + 5);
                                            }
                                            $hourStart = $timeStart->hour;
                                            if ($timeStart->day != $dateSelect->day) {
                                                $hourStart += 24;
                                            }
                                            $timeStartString = sprintf('%02d', $hourStart) . ':' . sprintf('%02d', $timeStart->minute);
                                        @endphp
                                        {{ $timeStartString }}
                                    @endif
                                </td>
                                @if (!isset($timeOrders))
                                    @if ($timeTableBegin->lessthan($dateStart))
                                        <td class="gray"></td>
                                    @elseif ($i == 1 && $timeTableBegin->equalTo($dateStart))
                                        @php
                                            $date = $dateStart->format('Y-m-d H:i:s');
                                        @endphp
                                        <td> 出勤 </td>
                                    @elseif (in_array($dateStart->format('Y-m-d H:i:s'), $timeCourseSuggest))
                                        <td>
                                            @if ($dateStart->diffInMinutes($date) - $timeAdd > 0)
                                                {{ $dateStart->diffInMinutes($date) - $timeAdd }}分
                                                OK
                                            @endif
                                        </td>
                                    @elseif($dateStart == $dateEnd)
                                        <td> 退勤 </td>
                                    @elseif($dateStart > $dateEnd)
                                        <td class="gray"></td>
                                    @else <td></td>
                                    @endif
                                @elseif (isset($timeOrders[$index]))
                                    @php
                                        $numerOrders = count($timeOrders);
                                    @endphp
                                    @if ($timeTableBegin->lessthan($dateStart))
                                        <td class="gray"></td>
                                    @elseif ($i == 1 && $timeTableBegin->equalTo($dateStart))
                                        @php
                                            $date = $dateStart->format('Y-m-d H:i:s');
                                        @endphp
                                        @if ($dateStart == $timeOrders[0]['timeStart'])
                                            <td style="background-color:{{ $timeOrders[$index]['colorId'] }}">
                                                {{ $timeOrders[$index]['customerName'] }}
                                                {{ $timeOrders[$index]['timeCourse'] }} 分
                                            </td>
                                        @else
                                            <td> 出勤 </td>
                                        @endif
                                    @elseif($date < $dateStart && $dateStart < $timeOrders[0]['timeStart'] &&
                                            (strtotime($timeOrders[0]['timeStart']) - strtotime($date))/60 - $timeAdd <
                                            $timeCourse[0]) <td class="gray">
                                            </td>
                                        @elseif($date < $dateStart && $dateStart < $timeOrders[0]['timeStart'] &&
                                                (strtotime($timeOrders[0]['timeStart']) - strtotime($date))/60 - $timeAdd>=
                                                $timeCourse[0])
                                                @if (in_array($dateStart->format('Y-m-d H:i:s'), $timeCourseSuggest))
                                                    <td>
                                                        @if ($dateStart->diffInMinutes($date) - $timeAdd > 0)
                                                            {{ $dateStart->diffInMinutes($date) - $timeAdd }}分
                                                            OK
                                                        @endif
                                                    </td>
                                                @elseif($dateStart == $dateEnd)
                                                    <td> 退勤 </td>
                                                @else <td></td>
                                                @endif
                                            @elseif ($timeOrders[$index]['timeStart'] <= $dateStart && $dateStart
                                                    <=$timeOrders[$index]['timeEnd']) <td
                                                    style="background-color:{{ $timeOrders[$index]['colorId'] }}">
                                                    @if ($dateStart == $timeOrders[$index]['timeStart'] || ($index < $numerOrders - 1 && $timeOrders[$index + 1]['timeStart'] == $dateStart))
                                                        {{ $timeOrders[$index]['customerName'] }}
                                                        {{ $timeOrders[$index]['timeCourse'] }} 分
                                                    @endif
                                                    @if ($dateStart == $timeRests[$index]['end'] && $index < $numerOrders - 1)
                                                        @php
                                                            $index++;
                                                        @endphp
                                                    @endif
                                                    </td>
                                                @elseif($timeRests[$index]['start'] <= $dateStart && $dateStart
                                                        <=$timeRests[$index]['end']) <td class="rest-time">
                                                        @if ($dateStart == $timeRests[$index]['end'] && $index < $numerOrders - 1)
                                                            @php
                                                                $index++;
                                                            @endphp
                                                        @endif
                                                        @if ($dateStart == $timeRests[$index]['middle'])
                                                            インターバル
                                                        @endif
                                                        </td>
                                                    @elseif($numerOrders == 1)
                                                        @if (in_array($dateStart->format('Y-m-d H:i:s'), $timeCourseSuggest))
                                                            <td>
                                                                @if ($dateStart <= $dateEnd && $dateStart->diffInMinutes($timeRests[0]['end']) - $timeAdd > 0)
                                                                    {{ $dateStart->diffInMinutes($timeRests[0]['end']) - $timeAdd }}分
                                                                    OK
                                                                @endif
                                                            </td>
                                                        @elseif($dateStart == $dateEnd)
                                                            <td> 退勤 </td>
                                                        @elseif($dateStart > $dateEnd)
                                                            <td class="gray"></td>
                                                        @else <td></td>
                                                        @endif
                                                    @elseif($index > 0 && $timeRests[$index-1]['end'] < $dateStart &&
                                                            $dateStart < $timeOrders[$index]['timeStart'] &&
                                                            (strtotime($timeOrders[$index]['timeStart']) -
                                                            strtotime($timeRests[$index - 1]['end']))/60 - $timeAdd * 2 <
                                                            $timeCourse[0]) <td class="gray">
                                                            </td>
                                                        @elseif($index > 0 &&
                                                            (strtotime($timeOrders[$index]['timeStart'])
                                                            - strtotime($timeRests[$index - 1]['end']))/60 - $timeAdd >
                                                            $timeCourse[0]
                                                            && $dateStart < $timeRests[$numerOrders -1]['end']) @if (in_array($dateStart->format('Y-m-d H:i:s'), $timeCourseSuggest))
                                                                <td>
                                                                    @if ($dateStart <= $dateEnd && $dateStart->diffInMinutes($timeRests[$index - 1]['end']) - $timeAdd > 0)
                                                                        {{ $dateStart->diffInMinutes($timeRests[$index - 1]['end']) - $timeAdd }}分
                                                                        OK @endif
                                                                </td>
                                                            @else <td></td>
                                    @endif
                                @elseif($dateStart > $timeRests[$numerOrders -1]['end'] &&
                                    $dateEnd->diffInMinutes($timeRests[$numerOrders -1]['end'])- $timeAdd >=
                                    $timeCourse[0]
                                    && $dateStart <= $dateEnd) @if (in_array($dateStart->format('Y-m-d H:i:s'), $timeCourseSuggest))
                                        <td>
                                            @if ($dateStart->diffInMinutes($timeRests[$numerOrders - 1]['end']) - $timeAdd > 0)
                                                {{ $dateStart->diffInMinutes($timeRests[$numerOrders - 1]['end']) - $timeAdd }}分
                                                OK
                                            @endif
                                        </td>
                                    @elseif($dateStart == $dateEnd)
                                        <td> 退勤 </td>
                                    @else <td></td>
                            @endif @elseif($dateStart> $dateEnd)
                                <td class="gray"></td>
                        @endif
                @endif
                </tr>
        @endfor
        </table>
        @endfor

    </div>
    @endif
    </div>
@endsection

@section('scripts')
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script src="{{ asset(mix('js/page_booking_cast_schedule.js')) }} "></script>
    <script src="{{ asset(mix('js/page_admin_app.js')) }} "></script>
    <script>
        var dateNowPicker = @json($dateSelect->toDateString());
    </script>
@endsection
