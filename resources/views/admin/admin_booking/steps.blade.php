<?php
use Illuminate\Support\Facades\Session;
use App\Constants\Booking as ConstantBooking;
?>

<div class="main-breadcrumb admin-booking">
    @php
        // tablet reservation
        $breadcrumbTablet = [
            [
                'title' => '予約受付トップ',
                'link' => route('admin.booking.cast'),
            ],
            [
                'title' => '日付選択',
                'link' => route('admin.booking.calendar'),
            ],
            [
                'title' => '時間／コース選択',
                'link' => route('admin.booking.course'),
            ],
            [
                'title' => 'ヒアリング',
                'link' => route('admin.booking.order'),
            ],
            [
                'title' => '予約内容確認',
                'link' => route('admin.booking.confirm'),
            ],
            [
                'title' => '決済',
                'link' => route('admin.booking.payment'),
            ],
        ];
        
        $breadcrumb = [];
        $sessionBooking = Session::get(ConstantBooking::SESSION_BOOKING_TYPE);
        
        if ($sessionBooking) {
            $breadcrumb = $breadcrumbTablet;
        } else {
            $active = 1;
            $buttonNext = false;
        }
    @endphp
    <div class="breadcrumb-menu">
        <div class="booking-breadcrumb">
            <div class="describe">現在の閲覧画面です。</div>
            <div class="btn-group btn-breadcrumb">
                @foreach ($breadcrumb as $key => $item)
                    <a href="{{ $item['link'] }}"
                        class="cast-btn cast-btn-warning
                            {{ $key + 1 == $active ? 'active' : '' }}
                            {{ $key + 1 > $active ? 'disabled' : '' }}
                        {{ $sessionBooking == ConstantBooking::NO_RESERVATION && $active == 2 && $active == $key ? 'active' : '' }}">
                        {{ $item['title'] }}
                    </a>
                    @php
                        if ($key + 1 == $active && array_key_exists($key + 1, $breadcrumb)) {
                            $urlPage = $breadcrumb[$key + 1]['link'];
                        }
                    @endphp
                @endforeach
                <!-- Button trigger modal -->
            </div>
        </div>
    </div>
    <div class="breadcrumb-buttons">
        <div class="text-right position-bottom">
            @if ($breadcrumb && $buttonNext)
                <div class="booking-btn-next">
                    <button type="button" class="btn" attr-url={{ $urlPage }} disabled>予約</button>
                </div>
            @endif
        </div>
    </div>
</div>
