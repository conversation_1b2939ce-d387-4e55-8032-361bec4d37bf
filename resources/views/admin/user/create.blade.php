@inject('mUserRole', 'App\Models\UserRole')
@extends('admin.layouts.app')

@section('title', 'ユーザー管理')

@section('css')
    <link rel="stylesheet" href="{{ asset('css/admin.users.css') }}" type="text/css">
@endsection

@section('adminPageName')
    <h2>ユーザー管理</h2>
@endsection

@section('pageNameBreadcrumb')
    <div class="breadcrumb">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.home.index') }}">管理トップページ</a></li>
                <li class="breadcrumb-item"><a href="{{ route('admin.users.index') }}">ユーザー管理</a></li>
                <li class="breadcrumb-item active" aria-current="page">ユーザー登録</li>
            </ol>
        </nav>
    </div>
@endsection
@section('content')
    <div class=" mt-3 manage-user create-user ">
        <h3 class="text">基本情報</h3>
        <div>
            <form action="{{ route('admin.users.store') }}" method="POST" id="form-create-user">
                @csrf
                <div class="row">
                    <div class="form-group col-md-6">
                        <label for=""> 名前 </label>
                        <input type="text" name="name" class="form-control"
                            value=" {{ old('name') ? old('name') : '' }} ">
                        @error('name')
                            <div class="alert alert-danger">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group col-md-6">
                        <label for="">ユーザー名</label>
                        <input type="text" name="account" class="form-control"
                            value="{{ old('account') ? old('account') : '' }}" autocomplete="off">
                        @error('account')
                            <div class="alert alert-danger">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="row">
                    <div class="form-group col-md-6 password">
                        <label for=""> パスワード </label>
                        <input type="password" name="password_new" class="form-control"
                            value="{{ old('password_new') ? old('password_new') : '' }}" autocomplete="new-password">
                        <i class="fa fa-eye" aria-hidden="true"></i>
                        <i class="fa fa-eye-slash" aria-hidden="true"></i>
                        @error('password_new')
                            <div class="alert alert-danger">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group col-md-6 password">
                        <label for="">パスワード再確認</label>
                        <input type="password" name="password_confirm" class="form-control"
                            value="{{ old('password_confirm') ? old('password_confirm') : '' }}">
                        <i class="fa fa-eye" aria-hidden="true"></i>
                        <i class="fa fa-eye-slash" aria-hidden="true"></i>
                        @error('password_confirm')
                            <div class="alert alert-danger">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <h3 class="text-center mb-3">権限割り当て選択</h3>
                <div class="table-permission">
                    <div class="table-list table-responsive">
                        <table class="table borderless">
                            @forelse ($groupPageRoute as $keyPage => $item)
                                <tr>
                                    <td>{{ $item['label'] }}</td>
                                    <td class="type-permission">
                                        <div>
                                            <input type="radio" name="permission_{{ $keyPage }}"
                                                id="inputAccess{{ $keyPage }}"
                                                value="{{ $mUserRole::TYPE_ACCESS }}" @php
                                                    $checked = '';
                                                    if (old('permission_{{ $keyPage }}')) {
                                                        if (old('permission_{{ $keyPage }}') == $mUserRole::TYPE_ACCESS) {
                                                            $checked = 'checked';
                                                        }
                                                    } else {
                                                        $checked = 'checked';
                                                    }
                                                @endphp
                                                {{ $checked }}>
                                            <label for="inputAccess{{ $keyPage }}">アクセス不可</label>
                                        </div>
                                    </td>
                                    <td class="type-permission">
                                        <div>
                                            <input type="radio" name="permission_{{ $keyPage }}"
                                                value="{{ $mUserRole::TYPE_VIEW }}" id="inputView{{ $keyPage }}"
                                                {{ old("permission_$keyPage") == $mUserRole::TYPE_VIEW ? 'checked' : '' }}>
                                            <label for="inputView{{ $keyPage }}">閲覧のみ</label>
                                        </div>
                                    </td>
                                    <td class="type-permission">
                                        <div>
                                            <input type="radio" name="permission_{{ $keyPage }}"
                                                value="{{ $mUserRole::TYPE_EDIT }}" id="inputEdit{{ $keyPage }}"
                                                {{ old("permission_$keyPage") == $mUserRole::TYPE_EDIT ? 'checked' : '' }}>
                                            <label for="inputEdit{{ $keyPage }}">編集</label>
                                        </div>
                                    </td>
                                </tr>
                            @empty

                            @endforelse
                        </table>
                    </div>
                </div>

            </form>
            <div class="button-form text-center">
                <a href="{{ route('admin.users.index') }}" class="btn  btn-cancel">キャンセル</a>
                <button type="submit" class="btn btn-save btn-save-create">保存</button>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script src="{{ assetMix('js/page_admin_user.js') }} "></script>
@endsection
