@extends('admin.layouts.app')

@section('title', 'ユーザー管理')

@section('css')
    <link rel="stylesheet" href="{{ assetMix('css/admin.users.css') }}" type="text/css">
@endsection

@section('adminPageName')
    <h2 class="page-name-text">ユーザー管理</h2>
@endsection

@section('pageNameBreadcrumb')
    <div class="breadcrumb">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.index') }}">管理トップページ</a></li>
                <li class="breadcrumb-item active" aria-current="page">ユーザー管理</li>
            </ol>
        </nav>
    </div>
@endsection

@section('content')
    <div class="list-users">
        <div class="row">
            <div class="col-6">
            </div>
            <div class="col-6 text-right">
                <a href="{{ route('admin.users.create') }}" class="btn btn-warning">ユーザー登録</a>
            </div>
        </div>
        <div class="table-list table-responsive">
            <table class="table table-hover table-shadown-nborder text-center">
                <thead>
                    <tr>
                        <th class="stt">ID</th>
                        <th>名前</th>
                        <th>ユーザー名</th>
                        <th>状態</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($users as $user)
                        <tr data-id="{{ $user->id }}">
                            <td> {{ $user->id }}</td>
                            <td> {{ $user->name }}</td>
                            <td> {{ $user->email }}</td>
                            <td class="col-status"> {{ $user->status_text }}</td>
                            <td class="table-actions">
                                <a href="{{ route('admin.users.edit', $user->id) }}" class="table-action-edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <i class="fa fa-refresh fa-spin hidden" aria-hidden="true"></i>
                                @if ($user->id !== 1)
                                    <a href="{{ route('admin.user.update.status', ['id' => $user->id]) }}"
                                        class="table-action-active disabled">
                                        <i class="{{ $user->status ? 'fa fa-unlock' : 'fa fa-lock' }}"
                                            aria-hidden="true"></i>
                                    </a>
                                @else
                                    <i class="fa fa-lock opacity-hidden" aria-hidden="true"></i>
                                @endif
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
            {{ $users->links() }}
        </div>
    </div>
@endsection

@section('scripts')
    <script src="{{ assetMix('js/page_admin_user.js') }} "></script>
@endsection
