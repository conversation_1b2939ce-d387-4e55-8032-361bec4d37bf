@inject('modelCourse', 'App\Models\Course')

@extends('admin.layouts.app')

@section('title', 'コース編集')

@section('css')
    <link rel="stylesheet" href="{{ assetMix('css/admin.edit.course.css') }}" type="text/css">
@endsection

@section('adminPageName')
    <h2 class="page-name-text">コース管理</h2>
@endsection

@section('pageNameBreadcrumb')
    <div class="breadcrumb">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.index') }}">管理トップページ</a></li>
                <li class="breadcrumb-item active" aria-current="page">コース編集</li>
            </ol>
        </nav>
    </div>
@endsection

@section('content')
    <div id="manage-create-course">
        <div class="row">
            <div class="col-md-12">
                @include('admin.course.breadcrumb')
            </div>
        </div>
        <div class="row date-course">
            <div class="col-md-6">
                <p class="date-update">最終更新日</p>
                <div class="date">
                    {{ formatDateJp($course->updated_at) }}
                </div>
            </div>
            <div class="col-md-6">
                <div class="float-right">
                    <p class="date-update">登録日 <span>{{ formatDateJp($course->created_at) }}</span></p>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <p class="notification">※編集完了後、既に、売上管理に表記されているこのコースの料金は、凍結されます。</p>
            </div>
        </div>
        <div class="row">
            <form method="POST" action="{{ route('admin.course.update', $course->id) }}" class="create-course">
                @method('put')
                <div class="form-group row range1">
                    <div class="col-md-3 label-center">
                        表示／非表示
                    </div>
                    <div class="col-md-3 label-center">
                        コース名
                    </div>
                    <div class="col-md-3 label-center">
                        時間
                    </div>
                    <div class="col-md-3 label-center">
                        料金
                    </div>
                    <div class="col-md-12">
                        <hr>
                    </div>
                    <div class="col-md-3 label-center">
                        <select class="display" name="status">
                            <option value="0">非表示</option>
                            <option value="1" {{ $course->status == 1 ? 'selected' : '' }}>表示</option>
                        </select>
                    </div>
                    <div class="col-md-3 label-center">
                        <input type="text" name="name" value="{{ $course->name ? $course->name : old('name') }}"
                            minlength="5" maxlength="15">
                        <div class="alert-error">{{ $errors->first('name') }}</div>
                    </div>
                    <div class="col-md-3 label-center">
                        <input type="text" name="time" data-type="currency"
                            value="{{ $course->time ? $course->time : old('time') }}" maxlength="3"> 分
                    </div>
                    <div class="col-md-3 label-center">
                        <input type="text" data-type='currency' class="price" name="price"
                            value="{{ $course->price ? $course->price : old('price') }}" maxlength="11"> 円
                    </div>
                </div>
                <div class="form-group row range1">
                    <div class="col-md-3 label-center">
                        キャストへの支払い
                    </div>
                    <div class="col-md-3 label-center">
                        初めて
                    </div>
                    <div class="col-md-3 label-center">
                        初めてではない
                    </div>
                    <div class="col-md-3 label-center">
                        フリー
                    </div>
                    <div class="col-md-12">
                        <hr>
                    </div>
                    <div class="col-md-3 label-center">
                    </div>
                    <div class="col-md-3 label-center">
                        <input type="text" data-type='currency' class="price-cast" name="price_cast"
                            value="{{ $course->price_cast ? $course->price_cast : old('price_cast') }}" maxlength="11"> 円
                        <div class="alert-error">{{ $errors->first('price_cast') }}</div>
                        <div class="alert-custom">※コースの基本料金より小さい数字を入力してください。</div>
                    </div>
                    <div class="col-md-3 label-center">
                        <input type="text" data-type='currency' class="price-cast-no-first" name="price_cast_no_first"
                            value="{{ $course->price_cast_no_first ? $course->price_cast_no_first : old('price_cast_no_first') }}"
                            maxlength="11"> 円
                        <div class="alert-error">{{ $errors->first('price_cast_no_first') }}</div>
                        <div class="alert-custom-no-first">※コースの基本料金より小さい数字を入力してください。</div>
                    </div>
                    <div class="col-md-3 label-center">
                        <input type="text" data-type='currency' class="price-cast-free" name="price_cast_free"
                            value="{{ $course->price_cast_free ? $course->price_cast_free : old('price_cast_free') }}"
                            maxlength="11"> 円
                        <div class="alert-error">{{ $errors->first('price_cast_free') }}</div>
                        <div class="alert-custom-free">※コースの基本料金より小さい数字を入力してください。</div>
                    </div>
                </div>
                <div class="form-group row range1">
                    <div class="col-md-5 apply">
                        <input type="radio" name="tax_included" class="no-apply-in-store" value="1"
                            {{ $course->tax_included == $modelCourse::TAX_INCLUDED ? 'checked' : '' }}>
                        <label>税込表示</label>
                    </div>
                    <div class="col-md-4 apply">
                        <input type="radio" name="tax_included" class="apply-in-store" value="0"
                            {{ $course->tax_included == $modelCourse::NO_TAX_INCLUDED ? 'checked' : '' }}>
                        <label>税抜き表示</label>
                        <div class="ml-6 custom">
                            <input type="checkbox" name="apply_in_store" class="apply-in-store" value="1"
                                {{ $course->apply_in_store == $modelCourse::APPLY_IN_STORE ? 'checked' : '' }}>
                            <label>店頭用</label>
                        </div>
                    </div>
                </div>
                <div class="form-group row range1">
                    <div class="col-md-6 position-delete">
                        <input type="checkbox" name="delete" class="delete" value="1">
                    </div>
                    <div class="col-md-6">
                        <label class="custom">削除</label>
                    </div>
                </div>
                <div class="form-group row range1">
                    <br>
                    <label class="col-sm-4 col-form-label date-public">公開日設定</label>
                    <div class="col-sm-8">
                        <span class="input-time">
                            <select name='year_public' class="year-public" id="year-custom">
                            </select>年
                            <select name='month_public' class="month-public" id="month-custom">
                            </select>月
                            <select name='day_public' class="day-public" id="day-custom">
                            </select>日
                            @if ($datePubic)
                                <input type="hidden" id="year"
                                    value="{{ $datePubic->format('Y') ? $datePubic->format('Y') : old('year_public') }}">
                                <input type="hidden" id="month"
                                    value="{{ $datePubic->format('m') ? $datePubic->format('m') : old('month_public') }}">
                                <input type="hidden" id="day"
                                    value="{{ $datePubic->format('d') ? $datePubic->format('d') : old('day_public') }}">
                            @endif
                        </span>
                        <div class="alert-error-1">※公開日のすべてを指定してください。</div>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-12">
                        <button class="btn btn-manage2" type="button">編集完了</button>
                    </div>
                </div>
                <input type="hidden" id="id" name="id" value="{{ $course->id }}">
                @csrf
            </form>
        </div>
    </div>
@endsection

@section('scripts')
    <script src="{{ assetMix('js/page_admin_course.js') }} "></script>
@endsection
