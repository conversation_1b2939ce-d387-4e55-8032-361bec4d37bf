<a href="#" class="swipper-button-close"></a>
<div class="swiper-container popup-preview-cast-images-swipper">
    <div class="swiper-wrapper">
        <div class="embrace">
            <div class="date">
                <div class="title date-start">
                    出勤予定時間を指定してください
                </div>
                <div class="title date-end">
                    退勤予定時間を指定してください
                </div>
            </div>
            <div class="time">
                <div class="time-start">
                    <select name="hour_start" class="hour-start">
                        @for ($i = BUSINESS_HOURS; $i < CLOSE_HOURS; $i++)
                            <option value="{{ $i < 10 ? '0' . $i : $i }}">{{ $i < 10 ? '0' . $i : $i }}</option>
                        @endfor
                    </select>
                    <select name="minute_start" class="minute-start">
                        @for ($i = 0; $i < 60; $i++)
                            <option value="{{ $i < 10 ? '0' . $i : $i }}">{{ $i < 10 ? '0' . $i : $i }}</option>
                        @endfor
                    </select>
                </div>
                <div class="middle">
                    ~
                </div>
                <div class="time-end">
                    <select name="hour_end" class="hour-end">
                        @for ($i = BUSINESS_HOURS; $i < CLOSE_HOURS; $i++)
                            <option value="{{ $i < 10 ? '0' . $i : $i }}">{{ $i < 10 ? '0' . $i : $i }}</option>
                        @endfor
                    </select>
                    <select name="minute_end" class="minute-end">
                        @for ($i = 0; $i < 60; $i++)
                            <option value="{{ $i < 10 ? '0' . $i : $i }}">{{ $i < 10 ? '0' . $i : $i }}</option>
                        @endfor
                    </select>
                </div>
            </div>
            <div class="alert">
                ※適切な時間を指定してください
            </div>
            <div class="alert-1">
                ※キャストの勤務時間を編集したら、部屋廻し管理で部屋の使用時間を再設定してください。
            </div>
            <div class="alert-4">
                ※この時間内、既に予約が入っている為、時間の変更は不可です。
            </div>
            <div class="selection">
                <div class="room">
                    <input type="checkbox" name="share_room" class="share-room" value="1">
                    <label>部屋廻し対象キャストとして指定する</label>
                </div>
                <div class="off">
                    <input type="checkbox" name="day_off" class="day-off" value="1">
                    <label>休日に指定する</label>
                </div>
            </div>
            <div class="alert-2">
                ※部屋廻し対象のキャストを変更すると、キャストの部屋番号を再設定必要がございます。
            </div>
            <div class="delete">
                <input type="checkbox" name="delete_schedule" class="delete-schedule" value="1">
                <label>削除する</label>
            </div>
        </div>
    </div>
</div>
