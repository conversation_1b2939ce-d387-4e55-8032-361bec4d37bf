@inject('mOrder', 'App\Models\Order' )
@inject('mCast', 'App\Models\Cast' )
@inject('mCourse', 'App\Models\Course' )
@inject('orderService', 'App\Services\OrderService')
@php
use Carbon\Carbon;
use App\Constants\Orders;
$now = Carbon::now();
if ($now->hour < CLOSE_HOURS - 24) {
    $now->subDay();
}
@endphp

@extends('admin.layouts.app')

@section('css')
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <link rel="stylesheet" href="{{ assetMix('/css/admin.general.manage.css') }}" type="text/css">
@endsection

@section('title', '総合管理')

@section('adminPageName')
    <h2>総合管理</h2>
@endsection

@section('pageNameBreadcrumb')
    <div class="breadcrumb">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.index') }}">管理トップページ</a></li>
                <li class="breadcrumb-item active" aria-current="page"> 総合管理 </li>
            </ol>
        </nav>
    </div>
@endsection

@section('content')
    <div class="managegeneral">
        <div class="col-md-12 d-flex-space">
            <div class="date">
                <div id="date-select"></div>
                <div class="img-calendar">
                    <form action="{{ route('admin.general.manage') }}" method="GET" id="search-calendar">
                        <div class="date-choose">
                            <img src="{{ asset('images/icons/calender_clock.png') }}" alt="calender clock"
                                class="datepicker img-datepicker">
                            <div class="text-date">{{ formatDateJp($dateSelect) }}</div>
                            <input type="date" name="date" class="hidden"
                                value="{{ $dateSelect->toDateString() }}">
                        </div>
                    </form>
                </div>
            </div>
            <div class="total-price">
                <div class="line">
                    <div class="line-price">
                        <div class="text">現金売上</div>
                        <div class="price">
                            {{ number_format($priceOrdersByCash - $priceOrderForCast) }}
                        </div>
                    </div>
                    <div class="unit">円</div>
                </div>
                <div class="line">
                    <div class="line-price">
                        <div class="text">キャストへの支払い</div>
                        <div class="price">
                            {{ number_format($priceForCast) }}</div>
                    </div>
                    <div class="unit">円</div>
                </div>
            </div>
        </div>
        <div class="col-md-12 note-shop">
            <div class="edit-note-shop d-none">
                <form action="{{ route('admin.general.note.shop') }}" method="POST" id="change-note-shop">
                    @csrf
                    <input type="hidden" name="date" value="{{ $dateSelect->toDateString() }}" />
                    <textarea id="shop-note" name="content" rows="5" style="color: {{ $shopNoteColor }}">{{ $shopNote }}</textarea>
                    <div class="option-note">
                        <input type="color" name="content_color" id="content-color" value="{{ $shopNoteColor }}"/>
                        <button class="btn btn-save" id="btn-save" type="submit">編集完了</button>
                    </div>
                </form>
            </div>
            <div class="view-note-shop">
                <div class="note-content" style="color: {{ $shopNoteColor }}">{{ $shopNote }}</div>
                <button class="btn btn-edit" id="btn-edit" type="button">編集</button>
            </div>
        </div>
        <div class="col-md-12 data-manage">
            <div class="all-status-order">
                <div class="status-order"><div class="square no-happen-yet"></div>&nbsp;予約</div>
                <div class="status-order"><div class="square confirmed-phone"></div>&nbsp;各電</div>
                <div class="status-order"><div class="square happening"></div>&nbsp;案内中</div>
                <div class="status-order"><div class="square success"></div>&nbsp;10 分前コール</div>
                <div class="status-order"><div class="square ten-minute-before"></div>&nbsp;完了</div>
            </div>
            @if (count($dataGeneral) <= 0)
                <p class="text-center">※該当なデータがありません</p>
            @else
                <div class="all-data-cast">
                    @foreach ($dataGeneral as $calendar)
                        <div class="showtable table-responsive">
                            <table class="table table-hover table-shadown">
                                <tbody>
                                    @php
                                        $dateStart = Carbon::parse($calendar->date_start);
                                        $dateEnd = Carbon::parse($calendar->date_end);
                                        if ($calendar->actual_date_start) $dateStart = Carbon::parse($calendar->actual_date_start);
                                        if ($calendar->actual_date_end) $dateEnd = Carbon::parse($calendar->actual_date_end);
                                        $hourStart = $dateStart->hour;
                                        $hourEnd = $dateEnd->hour;
                                        if ($dateStart->hour < CLOSE_HOURS - 24) {
                                            $hourStart = $dateStart->hour + 24;
                                        }
                                        if ($dateEnd->hour < CLOSE_HOURS - 24) {
                                            $hourEnd = $dateEnd->hour + 24;
                                        }
                                        $supportPrice = 0;
                                        $totalPriceOrderOfCast = 0;

                                        foreach ($calendar->orders as $key => $order) {
                                            $supportPrice += $order->support_price;
                                            $totalPriceOrderOfCast += $order->is_pay ? $order->order_price_cast : 0;
                                        }
                                        $expensesPrice = $totalPriceOrderOfCast * $calendar->expenses / 100;
                                    @endphp
                                    <tr>
                                        <td rowspan="2" colspan="2" class="name-time-cast">
                                            <div class="name-cast">{{ $calendar->name_cast }}</div>
                                            <div class="time-cast">
                                                <div class="time">
                                                    <div class="time-start">出</div>&nbsp;{{ sprintf('%02d', $hourStart) . ':' . sprintf('%02d', $dateStart->minute) }}
                                                </div>
                                                <div class="time">
                                                    <div class="time-end">退</div>&nbsp;{{ sprintf('%02d', $hourEnd) . ':' . sprintf('%02d', $dateEnd->minute) }}
                                                </div>
                                            </div>
                                        </td>
                                        <td rowspan="2" colspan="2">
                                            <div class="total-price-cast">
                                                <span>女子給</span>
                                                <span class="price-red">{{ number_format($totalPriceOrderOfCast - $expensesPrice - $calendar->dormitory_price - $calendar->total_price_livings) }} 円</span>
                                            </div>
                                        </td>
                                        <td>寮費</td>
                                        <td class="price-blue">{{ number_format($calendar->dormitory_price) }} 円</td>
                                        <td>小口</td>
                                        <td class="price-blue">{{ number_format($calendar->total_price_livings) }} 円</td>
                                        <td rowspan="2" colspan="2" class="title-note-cast">備考</td>
                                        <td rowspan="2" colspan="8" class="col-note-cast">
                                            <div class="edit-note-cast d-none">
                                                <div class="change-note-cast">
                                                    <input type="hidden" name="date" value="{{ $dateSelect->toDateString() }}" />
                                                    <input type="hidden" name="cast_id" value="{{ $calendar->cast_id }}" />
                                                    <textarea class="cast-note" name="content" rows="5">{{ $calendar->cast_note }}</textarea>
                                                </div>
                                            </div>
                                            <div class="view-note-cast">
                                                <div class="note-content">{{ $calendar->cast_note }}</div>
                                            </div>
                                        </td>
                                        <td rowspan="2" colspan="2" class="col-btn-note-cast">
                                            <div class="edit-note-cast d-none">
                                                <button class="btn btn-save btn-save-note-cast" type="button">編集完了</button>
                                            </div>
                                            <div class="view-note-cast">
                                                <button class="btn btn-edit btn-edit-note-cast" type="button">編集</button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>雑 費</td>
                                        <td class="price-blue">{{ number_format($expensesPrice) }} 円</td>
                                        <td>手 当</td>
                                        <td class="price-red">{{ number_format($supportPrice) }} 円</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="showtable table-responsive table-order">
                            <table class="table table-hover table-shadown">
                                <tbody>
                                    @for ($count = 0; $count < ceil(count($calendar->orders) / 10); $count++)
                                        <tr>
                                            @for ($i = 0; $i < 10; $i++)
                                                <td colspan="2" style="{{ isset($calendar->orders[$i + 10 * $count]) ? 
                                                    "background-color:" . getColorBackgroundOrder(
                                                        $calendar->orders[$i + 10 * $count]->status,
                                                        $calendar->orders[$i + 10 * $count]->is_ten_minutes_before,
                                                        $calendar->orders[$i + 10 * $count]->confirm_phone
                                                    ) : ''
                                                    }}">
                                                    @isset($calendar->orders[$i + 10 * $count])
                                                        @php
                                                            $orderDateStart = Carbon::parse($calendar->orders[$i + 10 * $count]->order_date_start);
                                                            $orderDateEnd = Carbon::parse($calendar->orders[$i + 10 * $count]->order_date_end);
                                                            if ($calendar->orders[$i + 10 * $count]->order_actual_date_start) {
                                                                if (!$calendar->orders[$i + 10 * $count]->order_actual_date_end) {
                                                                    $actualStart = Carbon::parse($calendar->orders[$i + 10 * $count]->order_actual_date_start);
                                                                    $orderDateEnd = $actualStart->addMinutes($orderDateEnd->diffInMinutes($orderDateStart));
                                                                }
                                                                $orderDateStart = Carbon::parse($calendar->orders[$i + 10 * $count]->order_actual_date_start);
                                                            }
                                                            if ($calendar->orders[$i + 10 * $count]->order_actual_date_end) {
                                                                $orderDateEnd = Carbon::parse($calendar->orders[$i + 10 * $count]->order_actual_date_end);
                                                            }
                                                            $orderHourStart = $orderDateStart->hour;
                                                            $orderHourEnd = $orderDateEnd->hour;
                                                            if ($orderDateStart->hour < CLOSE_HOURS - 24) {
                                                                $orderHourStart = $orderDateStart->hour + 24;
                                                            }
                                                            if ($orderDateStart->hour < CLOSE_HOURS - 24) {
                                                                $orderHourStart = $orderDateStart->hour + 24;
                                                            }
                                                        @endphp
                                                        <div>
                                                            {{ sprintf('%02d', $orderHourStart) . ':' . sprintf('%02d', $orderDateStart->minute) }}
                                                            {{ $calendar->orders[$i + 10 * $count]->status != $mOrder::STATUS_NO_HAPPEN_YET ? ' ～ ' . sprintf('%02d', $orderHourEnd) . ':' . sprintf('%02d', $orderDateEnd->minute) : '' }}
                                                            　{{ $calendar->orders[$i + 10 * $count]->customer_name }}
                                                        </div>
                                                    @endisset
                                                </td>
                                            @endfor
                                        </tr>
                                        <tr>
                                            @for ($i = 0; $i < 10; $i++)
                                                <td colspan="2" style="{{ isset($calendar->orders[$i + 10 * $count]) ?
                                                    "background-color:" . getColorBackgroundOrder(
                                                        $calendar->orders[$i + 10 * $count]->status,
                                                        $calendar->orders[$i + 10 * $count]->is_ten_minutes_before,
                                                        $calendar->orders[$i + 10 * $count]->confirm_phone
                                                    ) : ''
                                                    }}">
                                                    @isset($calendar->orders[$i + 10 * $count])
                                                        <div>{{ $calendar->orders[$i + 10 * $count]->course_time }} {{ @Orders::LABEL_TYPE_FEE[$calendar->orders[$i + 10 * $count]->type_fee] }} {{ $calendar->orders[$i + 10 * $count]->is_minute_add ? 's+' : '' }}</div>
                                                    @endisset
                                                </td>
                                            @endfor
                                        </tr>
                                        <tr>
                                            @for ($i = 0; $i < 10; $i++)
                                                <td colspan="2" style="{{ isset($calendar->orders[$i + 10 * $count]) ? 
                                                    "background-color:" . getColorBackgroundOrder(
                                                        $calendar->orders[$i + 10 * $count]->status,
                                                        $calendar->orders[$i + 10 * $count]->is_ten_minutes_before,
                                                        $calendar->orders[$i + 10 * $count]->confirm_phone
                                                    ) : ''
                                                    }}">
                                                    @isset($calendar->orders[$i + 10 * $count])
                                                        <div>{{ $calendar->orders[$i + 10 * $count]->is_pay ? number_format($calendar->orders[$i + 10 * $count]->order_price_cast) : 0 }} 円</div>
                                                    @endisset
                                                </td>
                                            @endfor
                                        </tr>
                                    @endfor
                                </tbody>
                            </table>
                        </div>
                    @endforeach
                </div>
            @endif
        </div>
        <input name="popup" value="{{ session('msg') ? session('msg') : '' }}" type="hidden">
    </div>
@endsection

@section('scripts')
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script>
        var dateNowPicker = @json($dateSelect->toDateString());
        var order = null;
    </script>
    <script src="{{ assetMix('js/page_admin_general_manage.js') }} "></script>
@endsection
