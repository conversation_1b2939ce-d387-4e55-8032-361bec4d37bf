@extends('admin.layouts.app')

@section('title', '設定管理')

@section('css')
    <link rel="stylesheet" href="{{ assetMix('css/admin.setting.css') }}" type="text/css">
@endsection

@section('adminPageName')
    <h2>設定管理</h2>
@endsection

@section('pageNameBreadcrumb')
    <div class="breadcrumb">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.home.index') }}">管理トップページ</a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">設定管理</li>
            </ol>
        </nav>
    </div>
@endsection

@section('content')
    <div class="manage-setting" id="manage-setting">
        @include('admin.setting.group_button')
        <div class="row">
            <div class="col-md-12">
                <form method="POST" action="{{ route('admin.setting.update', $setting->id) }}">
                    @csrf
                    <input type="hidden" name="id" value="{{ $setting->id }}">
                    <div class="text-head">
                        <h2>基本設定</h2>
                        <div class="note">
                            ※ 編集完了後、既に、売上管理に計上されている料金は、凍結されます。
                        </div>
                    </div>
                    <div class="body">
                        <div class="div-body">
                            <div class="title">
                                税率設定
                            </div>
                            <div class="input">
                                <input class="form-control" type="text" name="tax_percentage"
                                    value="{{ old('tax_percentage', $setting->tax_percentage) }}" data-type="currency"
                                    maxlength="9" min='0'
                                    oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;%
                            </div>
                            <div class="alert alert-danger">
                                {{ $errors->first('tax_percentage') }}
                            </div>
                        </div>
                        <div class="div-body div-col">
                            <div class="col-sm-6">
                                <div class="title">
                                    スタートプラス設定
                                </div>
                                <div class="note">
                                    ※ キャストの出勤時間に予約をしてもらった場合、時間のみプラス〇分に設定が可能です。
                                </div>
                                <div class="input">
                                    <input class="form-control" type="text" name="minute_add"
                                        value="{{ old('minute_add', $setting->minute_add) }}" data-type="currency"
                                        maxlength="9" min='0'
                                        oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;分
                                </div>
                                <div class="alert alert-danger">
                                    {{ $errors->first('minute_add') }}
                                </div>
                            </div>
                            <div class="col-sm-3">
                                <div class="title-2">
                                    キャストへの支払い金額
                                </div>
                                <div class="input">
                                    <input class="form-control" type="text" name="price_minute_add"
                                        value="{{ old('price_minute_add', $setting->price_minute_add) }}"
                                        data-type="currency" maxlength="9" min='0'
                                        oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;円
                                </div>
                                <div class="alert alert-danger">
                                    {{ $errors->first('price_minute_add') }}
                                </div>
                            </div>
                        </div>
                        <div class="div-body div-col">
                            <div class="col-sm-3">
                                <div class="title">
                                    延長料金設定
                                </div>
                                <div class="input">
                                    <input class="form-control" type="text" name="minute_extension"
                                        value="{{ old('minute_extension', $setting->minute_extension) }}"
                                        data-type="currency" maxlength="9" min='0'
                                        oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;分
                                </div>
                                <div class="alert alert-danger">
                                    {{ $errors->first('minute_extension') }}
                                </div>
                            </div>
                            <div class="col-sm-3">
                                <div class="note note-title">
                                    ※ 税抜き料金を入力してください。
                                </div>
                                <div class="input">
                                    <input class="form-control" type="text" name="price" min='0'
                                        value="{{ old('price', $setting->price) }}" data-type="currency" maxlength="9"
                                        oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;円
                                </div>
                                <div class="alert alert-danger">
                                    {{ $errors->first('price') }}
                                </div>
                            </div>
                            <div class="col-sm-3">
                                <div class="title">
                                    キャストへの支払い金額
                                </div>
                                <div class="input">
                                    <input class="form-control" type="text" name="price_cast"
                                        value="{{ old('price_cast', $setting->price_cast) }}" data-type="currency"
                                        maxlength="9" min='0'
                                        oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;円
                                </div>
                                <div class="alert alert-danger">
                                    {{ $errors->first('price_cast') }}
                                </div>
                            </div>
                        </div>
                        <div class="div-body">
                            <div class="title">
                                キャストのインターバル設定
                            </div>
                            <div class="input">
                                <input class="form-control" type="text" name="interval" min='0'
                                    value="{{ old('interval', $setting->interval) }}" data-type="currency" maxlength="9"
                                    oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;分
                            </div>
                            <div class="alert alert-danger">
                                {{ $errors->first('interval') }}
                            </div>
                        </div>
                        <div class="div-body div-col">
                            <div class="col-sm-3">
                                <div class="title">
                                    使用ポイント設定
                                </div>
                                <div class="input">
                                    <input class="form-control" type="text" name="price_point"
                                        value="{{ old('price_point', $setting->price_point) }}" data-type="currency"
                                        maxlength="9" min='0'
                                        oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;pt/1
                                    円
                                </div>
                                <div class="alert alert-danger">
                                    {{ $errors->first('price_point') }}
                                </div>
                            </div>
                            <div class="col-sm-3">
                                <div class="title">
                                    貯めるポイント設定
                                </div>
                                <div class="input">
                                    <input class="form-control" type="text" name="point_price"
                                        value="{{ old('point_price', $setting->point_price) }}" data-type="currency"
                                        maxlength="9" min='0'
                                        oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;円/1pt
                                </div>
                                <div class="alert alert-danger">
                                    {{ $errors->first('point_price') }}
                                </div>
                            </div>
                        </div>
                        <div class="div-body">
                            <div class="title">
                                廻し部屋インターバル設定
                            </div>
                            <div class="input">
                                <input class="form-control" type="text" name="room_interval"
                                    value="{{ old('room_interval', $setting->room_interval) }}" data-type="currency"
                                    maxlength="9" min='0'
                                    oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;分
                            </div>
                            <div class="alert alert-danger">
                                {{ $errors->first('room_interval') }}
                            </div>
                        </div>
                        <div class="div-body div-col">
                            <div class="col-sm-3">
                                <div class="title">
                                    美装代 5-9日
                                </div>
                                <div class="input">
                                    <input class="form-control" type="text" name="price_decorate_room_1"
                                        value="{{ old('price_decorate_room_1', $setting->price_decorate_room_1) }}"
                                        data-type="currency" maxlength="9" min='0'
                                        oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;分
                                </div>
                                <div class="alert alert-danger">
                                    {{ $errors->first('price_decorate_room_1') }}
                                </div>
                            </div>
                            <div class="col-sm-3">
                                <div class="title">
                                    美装代 10-15日
                                </div>
                                <div class="input">
                                    <input class="form-control" type="text" name="price_decorate_room_2" min='0'
                                        value="{{ old('price_decorate_room_2', $setting->price_decorate_room_2) }}"
                                        data-type="currency" maxlength="9"
                                        oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;円
                                </div>
                                <div class="alert alert-danger">
                                    {{ $errors->first('price_decorate_room_2') }}
                                </div>
                            </div>
                            <div class="col-sm-3">
                                <div class="title">
                                    美装代 > 15日
                                </div>
                                <div class="input">
                                    <input class="form-control" type="text" name="price_decorate_room_3"
                                        value="{{ old('price_decorate_room_3', $setting->price_decorate_room_3) }}"
                                        data-type="currency" maxlength="9" min='0'
                                        oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">&ensp;円
                                </div>
                                <div class="alert alert-danger">
                                    {{ $errors->first('price_decorate_room_3') }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="button-save">
                        <button type="submit" name="button-save" class="btn btn-save">保存</button>
                    </div>
                    <input name="popup2" value="{{ session('popup2') ? '保存完了しました。' : '' }}" type="hidden">
                </form>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script src="{{ assetMix('js/page_admin_setting.js') }} "></script>
@endsection
