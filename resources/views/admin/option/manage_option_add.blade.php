@extends("admin.layouts.app")

@section('title', 'オプション登録')

@section('css')
    <link rel="stylesheet" href="{{ asset(mix('css/manage.option.css')) }}" type="text/css">
@endsection

@section('adminPageName')
    <h2>オプション登録</h2>
@endsection

@section('pageNameBreadcrumb')
    <div class="breadcrumb">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.home.index') }}">管理トップページ</a></li>
                <li class="breadcrumb-item active" aria-current="page">オプション登録</li>
            </ol>
        </nav>
    </div>
@endsection

@section('content')
    <div class="manage_option" id="manage-option">
        <div class="container-fluid">
            <div class="btn-header">
                <div class="row">
                    <div class="col-md-12">
                        <div class="group-button-top">
                            <div class="btn-group">
                                <a href="{{ route('admin.option.index') }}" class="btn btn-ct-primary"
                                    role="button">オプション管理トップ</a>
                                <a href="#" class="btn btn-ct-primary active-primary" role="button">オプション登録</a>
                                <a href="#" class="btn btn-ct-primary disabled" role="button">オプション編集</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="time-header">
                <div class="time-update">
                    <div>最終更新日</div>
                    <div>{{ formatDateJp(now()) }}</div>
                </div>
                <div class="time-create">
                    登録日 {{ formatDateJp(now()) }}
                </div>
            </div>
            <form method="POST" action="{{ route('admin.option.store') }}">
                @csrf
                <div class="head-radio">
                    @if (old('tax_included') == 0 && old('tax_included') != null)
                        <div class="col-md-6 radio">
                            <div class="tax_included">
                                <input type="radio" name="tax_included" value="1">
                                <label for="tax_included">税込表示</label>
                            </div>
                            <div class="tax_included">
                                <input type="radio" name="tax_included" value="0" checked>
                                <label for="tax_included">税抜表示</label>
                            </div>
                        </div>
                    @else
                        <div class="col-md-6 radio">
                            <div class="tax_included">
                                <input type="radio" name="tax_included" value="1" checked>
                                <label for="tax_included">税込表示</label>
                            </div>
                            <div class="tax_included">
                                <input type="radio" name="tax_included" value="0">
                                <label for="tax_included">税抜表示</label>
                            </div>
                        </div>
                    @endif
                    <div class="note price col-md-6">
                        <label class="text-none">基本料金&emsp;</label>※ 税抜き料金を入力してください。
                    </div>
                </div>
                <div class="input">
                    <div class="option-main">
                        <div class="name col-sm-6">
                            <label for="name">オプション名&emsp;&emsp;</label>
                            <input type="text" name="name_options[0]" class="form-control"
                                value="{{ old('name_options.0') }}" maxlength="100"
                                oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                            <div class="alert alert-danger">{{ $errors->first('name_options.0') }}</div>
                        </div>
                        <div class="price col-sm-6">
                            <label for="price">基本料金&emsp;</label>
                            <input type="text" name="price_options[0]" class="form-control"
                                value="{{ old('price_options.0') }}" data-type="currency" maxlength="9" min='0'
                                oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                            円
                            <div class="alert alert-danger"></div>
                        </div>
                    </div>

                    <div class="option-sub">
                        @for ($i = 1; $i <= 10; $i++)
                            <div class="sub">
                                <div class="name col-sm-6">
                                    <label for="name">
                                        <div class="tamgiac"></div>
                                    </label>
                                    <input type="text" name="name_options[{{ $i }}]" class="form-control"
                                        value="{{ old('name_options.' . $i) }}" maxlength="100"
                                        oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                                    <div class="alert alert-danger">{{ $errors->first("name_options.$i") }}</div>
                                </div>
                                <div class="price col-sm-6">
                                    <label for="price">基本料金にプラス&emsp;</label>
                                    <input type="text" name="price_options[{{ $i }}]" class="form-control"
                                        value="{{ old('price_options.' . $i) }}" data-type="currency" maxlength="9"
                                        min='0'
                                        oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                                    円
                                    <div class="alert alert-danger"></div>
                                </div>
                            </div>
                        @endfor
                    </div>
                </div>
                <div class="price-cast">
                    <label for="price-cast">キャストへの支払い料金</label>&ensp;
                    <input type="text" name="price_cast" value="{{ old('price_cast') }}" data-type="currency"
                        maxlength="9" min='0' class="form-control"
                        oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);">
                    円
                    <div class="alert alert-danger">{{ $errors->first('price_cast') }}</div>
                </div>
                <div class="input-time">
                    <label for="time">公開日設定：</label>
                    @include('views.select_time', [
                    'year' => old('year'),
                    'month' => old('month'),
                    'day' => old('day'),
                    ])
                    <div class="alert alert-danger">{{ $errors->first('day') }}</div>
                </div>
                <div class="btn-add">
                    <button class="btn btn-manage2" type="submit">登録</button>
                </div>
                <input name="popup2" value="{{ session('popup') ? '登録完了しました。' : '' }}" type="hidden">
            </form>
        </div>
    </div>
@endsection

@section('scripts')
    <script src="{{ assetMix('js/page_option.js') }} "></script>
@endsection
