@inject('constantCast', 'App\Constants\Cast')

@extends('admin.layouts.app')

@section('title', 'キャスト成績表')

@section('css')
    <link rel="stylesheet" href="{{ asset('css/admin.manage.cast.css') }}" type="text/css">
@endsection

@section('adminPageName')
    <h2 class="page-name-text">キャスト管理</h2>
@endsection

@section('pageNameBreadcrumb')
    <div class="breadcrumb">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.index') }}">管理トップページ</a></li>
                <li class="breadcrumb-item active" aria-current="page">キャスト成績表</li>
            </ol>
        </nav>
    </div>
@endsection

@section('content')
    <div class="manage-cast" id="achievement-cast">
        <div class="cast">
            <div class="row">
                <div class="col-md-12">
                    <div class="group-button-top">
                        @include('admin.manage_cast.group_button')
                    </div>
                </div>
            </div>
        </div>

        <form action="" id="form-achievement" method="GET" class="mt-1">
            <input type="hidden" value="{{ $date->format('Y') }}" name="year" />
            <input type="hidden" value="{{ $date->format('m') }}" name="month" />
            <div class="row">
                <div class="col-lg-12 col-xl-7">
                    <div class="search-month">
                        <div class="float-left">
                            <span class="year">
                                <a href="#" class="prev-year"><i class="fa fa-angle-left"></i></a>
                                {{ $date->format('Y') }}年
                                <a href="#" class="next-year"><i class="fa fa-angle-right"></i></a>
                            </span>
                            <span class="month">
                                @for ($i = 1; $i <= 12; $i++)
                                    <a
                                        class="month-button {{ sprintf('%02d', $i) == $date->format('m') ? 'selected' : '' }}">
                                        <span>{{ $i }}</span> 月
                                    </a>
                                @endfor
                            </span>
                        </div>
                    </div>
                </div>
                <div class="col-lg-12 col-xl-5">
                    <div class="form-inline">
                        <div class="search-list-cast">
                            <label class="find-cast">キャスト検索 </label>
                            <input type="text" name="name_cast" id="name_cast" class="form-control" maxlength="15"
                                oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);"
                                placeholder="登録されているキャスト名で検索"
                                value="{{ isset($filters['name_cast']) ? $filters['name_cast'] : '' }}">
                            <button type="submit" class="btn button-search">検索</button>
                        </div>
                        <div id="listCast"></div>
                    </div>
                </div>
            </div>
        </form>
        <div class="table-list table-responsive">
            <table class="table table-bordered table-hover text-center table-shadown">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>キャスト名</th>
                        <th class="row-down-up">
                            <p>指名 <span class="w-button"></span></p>
                            <div class="th-down-up">
                                <button class="th-down" value="desc" name="total_fee_appoint_direct">
                                    <i class="fa fa-sort-desc"></i>
                                </button>
                                <button class="th-up" value="asc" name="total_fee_appoint_direct">
                                    <i class="fa fa-sort-asc"></i>
                                </button>
                            </div>
                        </th>
                        <th class="row-down-up">
                            <p>ネット<span class="w-button"></span></p>
                            <div class="th-down-up">
                                <button class="th-down" value="desc" name="total_fee_net">
                                    <i class="fa fa-sort-desc"></i>
                                </button>
                                <button class="th-up" value="asc" name="total_fee_net">
                                    <i class="fa fa-sort-asc"></i>
                                </button>
                            </div>
                        </th>
                        <th class="row-down-up">
                            <p>パネル<span class="w-button"></span></p>
                            <div class="th-down-up">
                                <button class="th-down" value="desc" name="total_fee_panel">
                                    <i class="fa fa-sort-desc"></i>
                                </button>
                                <button class="th-up" value="asc" name="total_fee_panel">
                                    <i class="fa fa-sort-asc"></i>
                                </button>
                            </div>
                        </th>
                        <th class="row-down-up">
                            <p>フリー<span class="w-button"></span></p>
                            <div class="th-down-up">
                                <button class="th-down" value="desc" name="total_fee_free">
                                    <i class="fa fa-sort-desc"></i>
                                </button>
                                <button class="th-up" value="asc" name="total_fee_free">
                                    <i class="fa fa-sort-asc"></i>
                                </button>
                            </div>
                        </th>
                        <th class="row-down-up">
                            <p>合計本数<span class="w-button"></span></p>
                            <div class="th-down-up">
                                <button class="th-down" value="desc" name="total_fee">
                                    <i class="fa fa-sort-desc"></i>
                                </button>
                                <button class="th-up" value="asc" name="total_fee">
                                    <i class="fa fa-sort-asc"></i>
                                </button>
                            </div>
                        </th>
                        <th class="row-down-up">
                            <p>出勤日数<span class="w-button"></span></p>
                            <div class="th-down-up">
                                <button class="th-down" value="desc" name="count_date">
                                    <i class="fa fa-sort-desc"></i>
                                </button>
                                <button class="th-up" value="asc" name="count_date">
                                    <i class="fa fa-sort-asc"></i>
                                </button>
                            </div>
                        </th>
                        <th class="row-down-up">
                            <p>一日平均本数<span class="w-button"></span></p>
                            <div class="th-down-up">
                                <button class="th-down" value="desc" name="average">
                                    <i class="fa fa-sort-desc"></i>
                                </button>
                                <button class="th-up" value="asc" name="average">
                                    <i class="fa fa-sort-asc"></i>
                                </button>
                            </div>
                        </th>
                        <th class="row-down-up">
                            <p>支払い金額<span class="w-button"></span></p>
                            <div class="th-down-up">
                                <button class="th-down" value="desc" name="total_price_cast">
                                    <i class="fa fa-sort-desc"></i>
                                </button>
                                <button class="th-up" value="asc" name="total_price_cast">
                                    <i class="fa fa-sort-asc"></i>
                                </button>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($castCalendars as $key => $castCalendar)
                        <tr>
                            <td>{{ $castCalendar->id }}</td>
                            <td>{{ $castCalendar->name_cast }}</td>
                            <td>
                                {{ $castCalendar->total_fee_appoint_direct ? $castCalendar->total_fee_appoint_direct : 0 }}
                            </td>
                            <td>{{ $castCalendar->total_fee_net ? $castCalendar->total_fee_net : 0 }}</td>
                            <td>{{ $castCalendar->total_fee_panel ? $castCalendar->total_fee_panel : 0 }}</td>
                            <td>{{ $castCalendar->total_fee_free ? $castCalendar->total_fee_free : 0 }}</td>
                            <td>{{ $castCalendar->total_fee ? $castCalendar->total_fee : 0 }}</td>
                            <td>{{ $castCalendar->count_date ? $castCalendar->count_date : 0 }}</td>
                            <td>{{ $castCalendar->average ? number_format($castCalendar->average, 2) : number_format(0, 2) }}
                            </td>
                            <td>{{ $castCalendar->total_price_cast ? number_format($castCalendar->total_price_cast) : 0 }}
                                円</td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="11" class="color-red">※該当なデータ無し </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
            <div class="float-right">
                {{ $castCalendars->links() }}
            </div>
        </div>
    </div>

@endsection

@section('scripts')
    <script>
        var IdForm = 'form-achievement';
        var sortName = @json($filters['sort_name']);
        var sortType = @json($filters['sort_type']);
    </script>
    <script src="{{ assetMix('js/page_admin_manage_cast.js') }} "></script>
@endsection
