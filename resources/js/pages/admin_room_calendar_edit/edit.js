$(document).ready(function() {

    let date = $("input[name='date_time'").val();
    let tables = $('table').get();
    let arrIdSelect = idCasts;
    let idCastDelete = [];
    $('.cast_name').each(function(el) {
        if ($(this).val() == 0) {
            $(this).closest('tr').find('select.time').attr('disabled', 'disabled');
        }
    })
    axios.get(laroute.route('admin.cast.list.type'), {
        params: {
            date: date,
            type: NO_SHARE_ROOM
        }
    }).then(function(response) {
        $('.check-share:not(:checked)').closest('table').find('.cast_name').each(function(el) {
            addOptionNameCast(response.data.data, $(this));
        });
        axios.get(laroute.route('admin.cast.list.type', { date: date }), {
            params: {
                date: date,
                type: SHARE_ROOM
            }
        }).then(function(response) {
            $('.check-share:checked').closest('table').find('.cast_name').each(function(el) {
                addOptionNameCast(response.data.data, $(this));
            });
        });
    });

    function getTimer(el, name, roomId, slot) {

        return el.find(`select[name="hour_${name}[${roomId}][${slot}]"]`).val() + ':' +
            el.find(`select[name="minute_${name}[${roomId}][${slot}]"]`).val();
    }

    function getAllTimerByRoomId(roomId) {
        let timers = [];
        let room = $(`table[data-room-id="${roomId}"]`);
        [1, 2, 3].forEach((slot) => {

            let parent = room.find(`.hour[data-slot="${slot}"]`);;
            let timeStart = getTimer(parent, 'start', roomId, slot)
            let timeEnd = getTimer(parent, 'end', roomId, slot)

            timers.push({
                timeStart: timeStart,
                timeEnd: timeEnd,
            });

        })
        return timers;
    }

    function setError(el, message) {
        let parentTd = el.closest('td');
        let error = parentTd.find('div.error');
        error.html(message);
    }

    function checkSetTime(shift) {
        if (shift.timeStart.indexOf('-1') < 0 && shift.timeEnd.indexOf('-1') < 0) {
            return 1;
        }
        return 0;
    }

    function checkTimeShift(shift1, shift2, el, message) {
        if (checkSetTime(shift1) && checkSetTime(shift2)) {
            if (shift1.timeEnd > shift2.timeStart) {
                setError(el, message);
            }
        }
    }
    $('.cast_name').on('focus', function() {
        castIdOld = Number($(this).find(":selected").val());
        castNameOld = $(this).find(":selected").html();
        roomId = $(this).closest('table').attr('data-room-id');
    }).on('change', function(e) {
        if (arrIdSelect.indexOf(castIdOld) >= 0) {
            idCastDelete.push(castIdOld);
        }
        arrIdSelect = arrIdSelect.filter(item => item !== castIdOld);
        let castId = Number($(this).val());
        idCastDelete = idCastDelete.filter(item => item !== castId);
        let shift = $(this).attr('data-slot');
        if (castId == '0') {
            $(this).closest('td').find('p.error_name').html('');
            $(this).closest('table').find(`.hour[data-slot="${shift}"`).find('select').val("-1");
            $(this).closest('table').find(`.hour[data-slot="${shift}"`).find('select').attr('disabled', 'disabled');
        } else {
            $(this).closest('table').find(`.hour[data-slot="${shift}"`).find('select').removeAttr('disabled');
            if (arrIdSelect.indexOf(castId) >= 0) {
                $(this).closest('td').find('p.error_name').html('※他の部屋に登録されたキャストです。');
            } else {
                $(this).closest('td').find('p.error_name').html('');
                arrIdSelect.push(castId);
            }
        }
    })

    $("select.time").on('change', function(e) {
        var curent = $(this);
        let all = getAllTimerByRoomId($(this).closest('table').attr('data-room-id'));
        let tableParent = $(this).closest('table');
        let roomId = tableParent.attr('data-room-id');
        let shift = $(this).closest(".hour").attr('data-slot');
        let castId = tableParent.find(`select[name="cast_id[${roomId}][${shift}]"]`).val();
        let date = $("input[name='date_time'").val();
        if (checkSetTime(all[shift - 1])) {
            if (all[shift - 1].timeStart > all[shift - 1].timeEnd) {
                setError(curent, '※適切な時間を入力してください。');
            } else {
                setError(curent, "");
                let timeStart = all[shift - 1].timeStart;
                let timeEnd = all[shift - 1].timeEnd;
                axios.get(laroute.route('admin.check.time.cast'), {
                    params: {
                        date: date,
                        time_start: timeStart,
                        time_end: timeEnd,
                        cast_id: castId
                    }
                }).then(function(response) {
                    if (response.data.message == '※キャストの出勤時間外です。') {
                        setError(curent, '※キャストの出勤時間外です。');
                    } else {
                        setError(curent, "");
                        switch (shift) {
                            case '1':
                                checkTimeShift(all[0], all[1], curent, '※部屋の使用時間が被っています。');
                                checkTimeShift(all[0], all[2], curent, '※部屋の使用時間が被っています。');
                                break;
                            case '2':
                                checkTimeShift(all[0], all[1], curent, '※部屋の使用時間が被っています。');
                                checkTimeShift(all[1], all[2], curent, '※部屋の使用時間が被っています。');
                                break;
                            case '3':
                                checkTimeShift(all[1], all[2], curent, '※部屋の使用時間が被っています。');
                                checkTimeShift(all[0], all[2], curent, '※部屋の使用時間が被っています。');

                        }
                    }

                });
            }
        }
    });

    $(".mask").dblclick(function(e) {
        let table = $(this).closest('table');
        let check = table.find('.check-share').first();
        if (check.prop("checked") == true) {
            check.prop('checked', false);
            roomShare = 0;
            let select = table.find('select.cast_name');
            axios.get(laroute.route('admin.cast.list.type'), {
                params: {
                    date: date,
                    type: NO_SHARE_ROOM
                }
            }).then(function(response) {
                let html = `<option value="0" selected> </option>`;
                select.each(function(el) {
                    let cast1 = Number($(this).val());
                    arrIdSelect = arrIdSelect.filter(item => item !== cast1);
                    idCastDelete.push(cast1);
                    $(this).html(html);
                    addOptionNameCast(response.data.data, $(this));
                })
                table.find(`.hour`).find('select').attr('disabled', 'disabled');
                table.find('.error').html("");
            })
        }
    })

    $('.mask').click(function(e) {
        let table = $(this).closest('table');
        let check = table.find('.check-share').first();
        if (table.attr('data-room-id') != roomShare && roomShare) {
            showPopupYesNo('', '部屋廻しが設定されている為、本当に再設定したいですか？', 'OK', 'キャンセル', function() {
                check.click();
            });
        } else {
            check.click();
        }
    })

    var tableRomShareCurent = $('.check-share:checked').closest('table');
    $(".check-share").on('change', function(e) {
        let selectedOld = tableRomShareCurent.find("select.cast_name");
        [1, 2, 3].forEach((slot) => {
            let cast1 = Number(tableRomShareCurent.find(`.cast_name[data-slot="${slot}"]`).val());
            arrIdSelect = arrIdSelect.filter(item => item !== cast1);
            idCastDelete.push(cast1);
        })
        selectedOld.val('0');
        tableRomShareCurent.find(`.hour`).find('select').attr('disabled', 'disabled');
        tableRomShareCurent = $(this).closest('table');
        roomShare = tableRomShareCurent.attr('data-room-id');
        tableRomShareCurent.find(`.hour`).find('select').attr('disabled', 'disabled');
        [1, 2, 3].forEach((slot) => {
            let cast1 = Number(tableRomShareCurent.find(`.cast_name[data-slot="${slot}"]`).val());
            arrIdSelect = arrIdSelect.filter(item => item !== cast1);
            idCastDelete.push(cast1);
        });
        let select = tableRomShareCurent.find("select.cast_name");
        if ($(this).prop("checked")) {
            axios.get(laroute.route('admin.cast.list.type'), {
                params: {
                    date: date,
                    type: SHARE_ROOM
                }
            }).then(function(response) {
                let html = `<option value="0" selected> </option>`;
                select.each(function(el) {
                    $(this).html(html);
                    addOptionNameCast(response.data.data, $(this));

                })
                tableRomShareCurent.find('.error').html("");
            })
        }
        axios.get(laroute.route('admin.cast.list.type'), {
            params: {
                date: date,
                type: NO_SHARE_ROOM
            }
        }).then(function(response) {
            let html = `<option value="0" selected> </option>`;
            selectedOld.each(function(el) {
                $(this).html(html);
                addOptionNameCast(response.data.data, $(this));
            })
            tableRomShareCurent.find(`.hour`).find('select').attr('disabled', 'disabled');
            tableRomShareCurent.find('.error').html("");
        })

    });

    $('#form-edit-room').submit(function(e) {
        idCastDelete.forEach(element => {
            deleteRoomCalendar(element, date);
        });
    })

    function addOptionNameCast(data, el) {
        let html = ``;
        data.forEach(cast => {
            if (el.val() != cast.cast_id) {
                html +=
                    `<option value="${cast.cast_id}" hour_start="${cast.hour_start}"
                hour_end="${cast.hour_end}">
                 ${cast.name_cast}
                </option>`;
            }
        });
        el.append(html);
    }

    function deleteRoomCalendar(castId, date) {
        axios.delete(laroute.route(`admin.delete.room.calendar`, {
                cast_id: castId,
                date: date
            }))
            .then(function(response) {});
    }

    $('#showPopup').on('click', function(e) {
        let date = $(this).attr('date-time');
        window.open(laroute.route('admin.show.popup.cast', {
            date: date
        }));
    })

    $('.btn-save').click(function() {
        showPopupOk('', ' 編集完了しました。', 'OK', function() {
            $('#form-edit-room').submit();
        })
    })
});
