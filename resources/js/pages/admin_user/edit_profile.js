$(function() {
    if (typeof display == 'undefined') {
        display = false;
    }
    $('.change-password').on('click', function() {
        $('.form-change-password').toggle();
        if ($('.form-change-password').attr('style') == 'display: block;') {
            $('input[name=change_password]').val(1);
        } else {
            $('input[name=change_password]').val(0);
        }
    });

    let isPopUpYesNo = false;
    let passwordOld = $(`input[name='password_old']`);
    let passwordNew = $(`input[name='password_new']`);
    let passwordConfirm = $(`input[name='password_confirm']`);
    convertValue(passwordOld);
    convertValue(passwordNew);
    convertValue(passwordConfirm);

    passwordOld.on('change', function() {
        isPopUpYesNo = true;
    })
    passwordNew.on('change', function() {
        isPopUpYesNo = true;
    })
    passwordConfirm.on('change', function() {
        isPopUpYesNo = true;
    })
    if (passwordOld.val() || passwordNew.val() || passwordConfirm.val()) {
        isPopUpYesNo = true;
    }
    if (display) {
        $('.form-change-password').show();
    }

    $('.edit-profile-user .btn-save').on('click', function() {
        if (isPopUpYesNo) {
            if (!passwordOld.val()) {
                $('#error-password-old').html('※パスワードを入力してださい。');
            } else {
                showPopupYesNo('', ' 本当にパスワードを変更しますか?', 'OK', 'Cancel', function() {
                    $('#form-edit-profile').submit();
                });
            }
        } else {
            showPopupOk('', ' 編集完了しました。', 'OK', function() {
                $('#form-edit-profile').submit();
            })
        }
    });

    $('.btn-cancel').on('click', function() {
        window.location = laroute.route('admin.home.index');
    })

    function toASCII(chars) {
        var ascii = '';
        for (var i = 0, l = chars.length; i < l; i++) {
            var c = chars[i].charCodeAt(0);
            if (c >= 0xFF00 && c <= 0xFFEF) {
                c = 0xFF & (c + 0x20);
            }
            ascii += String.fromCharCode(c);
        }
        return ascii;
    }

    function convertValue(input) {
        input.on({
            blur: function() {
                var val = toASCII($(this).val());
                $(this).val(val);
            }

        });
    }
})
