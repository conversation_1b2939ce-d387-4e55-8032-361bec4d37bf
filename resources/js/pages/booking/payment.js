$('.booking-payment input[type="checkbox"]').on('click', function() {
    if ($(this).is(":checked")) {
        $('.booking-payment .box-invoice').find('div').removeClass('font-big');
        $('.booking-payment input[type="checkbox"]').not(this).prop('checked', false);
        if ($(this).val() == 2) {
            $('.booking-payment .show-more').addClass('font-big');
            $('.booking-payment .checkbox-percent').removeClass('hidden');
        } else {
            $('.booking-payment .checkbox-percent').addClass('hidden');
            $('.booking-payment .show-less').addClass('font-big');
        }
    } else {
        $(this).attr('checked', 'checked');
        $(this).prop('checked', true);
    }
});
var that = $('.booking-payment input[name="payment_2"]');
if (that.is(":checked")) {
    $('.booking-payment .box-invoice').find('div').removeClass('font-big');
    $('.booking-payment input[type="checkbox"]').not(that).prop('checked', false);
    if (that.val() == 2) {
        $('.booking-payment .show-more').addClass('font-big');
        $('.booking-payment .checkbox-percent').removeClass('hidden');
    } else {
        $('.booking-payment .checkbox-percent').addClass('hidden');
        $('.booking-payment .show-less').addClass('font-big');
    }
}

$('.booking-payment .btn-submit').on('click', function() {
    var type = $("input[type='checkbox']:checked").val();
    $(this).attr('disabled', 'disabled');
    var url = $(this).attr('attr-url');
    axios.post(url, {
            'type_payment': type
        })
        .then(res => {
            if (typeof res.data.reset != 'undefined' && res.data.reset) {
                showPopupOkBackdrop('', '<div class="text-center">' + res.data.message + '</div>', 'OK', function() {
                    window.location.href = laroute.route('booking.index');
                })
            } else if (res.data.success) {
                var message = '<p>予約が完了致しました。予約画面に戻してもよろしいですか。</p>';
                if (res.data.number) {
                    message = '<p>予約が完了しました</p>';
                    message += '<p>予約受付が重複する為、下記の新しい番号でお呼びいたします。</p>';
                    message += '<div class="confirm-notification-page"><div class="number">' + res.data.number + ' 番</div></div>';
                }
                showPopupOkBackdrop('', '<div class="text-center">' + message + '</div>', 'OK', function() {
                    window.location.href = laroute.route('booking.index');
                })
                $(this).removeAttr('disabled');
            } else {
                if (typeof res.data.url != 'undefined') {
                    window.location.href = res.data.url;
                } else {
                    showPopupOk("", res.data.message);
                }
            }
        }).catch(err => {
            showPopupOk("", 'システムエラー ');
            $(this).removeAttr('disabled');
        })
});


$('#page-show-orders .btn-confirm').on('click', function(event) {
    event.preventDefault();
    var totalCheck = 0;
    $('input[type=checkbox]:checked').each(function() {
        totalCheck++;
    })
    if (totalCheck) {
        $("#page-show-orders").submit();
    } else {
        showPopupOk('', '決済したい予約を選択してください。');
    }
})
