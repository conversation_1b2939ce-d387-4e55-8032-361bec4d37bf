require('./add.edit')

if (PERMISSION) {
    $('.app-content').find('a').removeAttr('href');
    $('.app-content').find('a.btn-ct-primary.action').addClass("disabled");
    $('.app-content').find('a.btn-primary.edit').addClass("disabled").css("color", "black").css('background-color', '#bcbcbc').css('border-color', '#bcbcbc');
    $('.app-content').find('a.btn-primary.list').addClass("disabled").css("color", "black").css('background-color', '#bcbcbc').css('border-color', '#bcbcbc');
}
