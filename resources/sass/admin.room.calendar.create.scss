@import "variables";
.manage-room {
    color: $textColor;
    .group-button-top {
        a.btn-ct-primary {
            width: 160px;
        }
    }
    .date {
        margin: 15px;
        font-size: 1rem;
    }
    .name-table {
        border-bottom: 4px solid $blue;
    }
    .calendar {
        margin-top: 5px;
    }
    .border-orange {
        border-bottom: 2px solid $yellow1;
        width: 23rem;
        margin-left: 0px;
    }
    #show-table {
        margin: 10px;
        .name-table {
            margin: 0px auto;
            text-align: center;
            font-size: 1rem;
        }
        .name-cast {
            text-align: center;
        }
        .select-date {
            margin-top: 30px;
            width: 100%;
            img {
                width: 60px;
                margin-left: -8px;
            }
            font-size: 16px;
            .cast-calendar {
                text-align: right;
                button {
                    border: 1px solid;
                    border-radius: 8px;
                    margin-right: -30px;
                    font-size: 1rem;
                }
            }
        }
        .list-table-room {
            width: 100%;
            .table-item {
                margin: 30px auto;
                .check-share {
                    margin: 90px 20px;
                }
                .name-room {
                    padding: 100px 10px;
                }
            }
        }
        td {
            text-align: center;
        }
        .checkbox {
            position: relative;
        }
        .mask {
            position: absolute;
            width: 13px;
            height: 13px;
            top: 43%;
            left: 44%;
        }
        select.cast_name {
            width: 8rem;
            height: 1.5rem;
            margin-top: 10px;
        }
        td div.hour {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 10px;
            select {
                width: 3rem;
                height: 1.5rem;
            }
            option {
                font-size: 16px;
            }
        }
        .error_name {
            width: 100%;
        }
        .date {
            display: flex;
            flex-direction: row;
        }
        .form-group button {
            padding: 10px 80px;
            border-radius: 40px;
        }
        button.submit {
            margin-top: 20px;
            color: $white;
            border: 2px solid $btnSave;
            border-radius: 20px;
            background-color: $btnSave;
        }
        .btn.submit.permission {
            background-color: $gray;
            border: none;
        }
    }
}
