@import "variables";
.select-course-page {
    color: black;
    margin-bottom: 20px;
    .text-head {
        display: flex;
        align-items: center;
        justify-content: center;
        align-content: center;
        p {
            border-bottom: 4px solid #00288fcc;
            font-size: 20px;
            font-weight: inherit;
            text-align: center;
            padding: 5px;
        }
    }
    p {
        margin-bottom: 5px;
    }
    .col-sm-3,
    .col-sm-4,
    .col-sm-5 {
        display: flex;
        align-items: center;
        align-content: center;
        justify-content: center;
    }
    .head {
        justify-content: space-between;
    }
    button {
        outline: none;
    }
    .row.time {
        display: flex;
    }
    .select-time {
        display: flex;
        justify-content: center;
        align-items: center;
        top: -35px;
    }
    select {
        border: 1px solid black;
        padding: 0 20px;
        color: black;
        cursor: pointer;
        text-align-last: left;
        font-size: 20px;
    }
    .select {
        position: relative;
        display: flex;
        height: 100%;
        line-height: 3;
        font-size: 15px;
    }
    .time-2 {
        margin: 0px 10px 0px 10px;
    }
    .row.option {
        margin: 0 0 10px 0;
        font-size: 16px;
        overflow: auto;
        max-height: 320px;
        .col-sm-6 {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .btn.select-course {
            display: flex;
            width: 100%;
            padding: 10px 0px;
            border-radius: 14px;
            height: 60px;
            margin: 0px 0px 20px 0px;
            align-items: center;
            color: #000;
        }
    }
    .row.option-2 {
        margin: 0;
        font-size: 16px;
        .col-sm-6 {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
    }
    input.add-time-checkbox {
        margin: 0;
        height: 24px;
        width: 24px;
    }
    label.add-time-lable {
        margin: 0 0 0 5px;
    }
    .row.steps {
        display: flex;
        width: 100%;
    }
    .step {
        padding: 4px;
        border: 2px solid #20acec;
        width: 100px;
        height: 30px;
        text-align: center;
        margin: 6px 3px 0 0px;
        font-size: 11px;
    }
    .step.selected {
        background-color: #9ac4e9;
    }
    .full {
        background: #5d5555b5;
    }
    h1 {
        color: black;
    }
    .add {
        margin: -3px 0 0 24px;
        width: 38px;
        height: 38px;
        text-align: center;
        border-radius: 1.375rem;
        border: 1px solid #cc2654;
    }
    .add p {
        margin: 14% 0 0 0;
    }
    a {
        color: black;
    }
    .name-cast {
        margin: 0 0 0 25%;
        width: 100%;
        p {
            font-size: 18px;
            font-weight: initial;
        }
    }
    .time-book {
        margin: 0 0 0 25%;
        width: 100%;
        display: flex;
        p {
            font-size: 18px;
            font-weight: initial;
        }
    }
    .icon-time {
        z-index: 1;
        a {
            margin: 4px 0 0 98px;
        }
        .img-cast-1 {
            width: 39px;
        }
    }
    .btn {
        font-size: 16px;
        font-weight: 400;
        line-height: 1.5;
        padding: 0;
        letter-spacing: 0;
    }
    .booking-btn-next {
        button {
            background-color: $orangeYellowH;
            border-color: $orangeYellowH;
        }
    }
    button:disabled,
    button[disabled] {
        background-color: #5d5555b5;
        border-color: #5d5555b5;
        pointer-events: none;
    }
    .fa-calendar {
        font-size: 55px;
    }
    .select-input {
        #hour-button,
        #minute-button {
            width: 87px;
            text-align: center;
            border: 1px solid $dark1;
            border-radius: .25rem;
            background-color: $white;
            box-shadow: 0 3px 2px rgb(233 236 239 / 5%);
            padding: 10px 0px;
            .ui-icon {
                width: 20px;
                height: 15px;
            }
        }
        .ui-selectmenu-button-open {
            border-color: $btColorHover !important;
            outline: 0;
            box-shadow: 0 3px 9px rgb(50 50 9 / 0%), 3px 4px 8px rgb(94 114 228 / 10%) !important;
        }
    }
    .add-time {
        display: none;
        flex-direction: row;
        align-content: center;
        justify-content: center;
        font-size: 16px;
        font-weight: 400;
        line-height: 1.5;
        padding: 0;
        letter-spacing: 0;
        max-width: 100%;
        flex: 0 0 50%;
    }
    .ui-icon-triangle-1-s {
        width: 7px;
        height: 7px;
        box-sizing: border-box;
        position: absolute;
        left: 80%;
        top: 33%;
        transform: rotate(135deg);
        &::before {
            content: '';
            width: 100%;
            height: 100%;
            border-width: 2px 2px 0 0;
            border-style: solid;
            border-color: black;
            transition: .2s ease;
            display: block;
            transform-origin: 100% 0;
        }
        &:after {
            content: '';
            float: left;
            position: relative;
            top: -100%;
            width: 100%;
            height: 100%;
            border-width: 0 2px 0 0;
            border-style: solid;
            border-color: black;
            transform-origin: 100% 0;
            transition: .2s ease;
        }
    }
    .button-submit {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        align-content: center;
        .submit {
            background-color: #9EF233;
            margin-top: 5%;
            border-radius: 1.375rem;
            border: 2px solid #312922;
            color: white;
            padding: 7px 6%;
        }
        .submit:disabled {
            background-color: #b4b7b7;
        }
    }
    .course {
        display: none;
    }
    .btn-course {
        .col-sm-4 {
            flex-direction: column;
            .text {
                font-size: 12px;
                font-style: italic;
            }
        }
    }
    .btn-course-disabled {
        background-color: $disabled !important;
        .col-sm-4 {
            flex-direction: column;
            .text {
                font-size: 12px;
                font-style: italic;
            }
        }
    }
    .overlay {
        position: fixed;
        left: 0;
        top: 0;
        width: 100vw;
        height: 100vh;
        display: none;
        background-color: #000;
        opacity: 0.5;
    }
    .popup {
        display: none;
        position: fixed;
        left: 50%;
        top: 20%;
        width: 400px;
        height: 200px;
        transform: translate(-50%, -50%);
        background-color: white;
        border-radius: 5px;
        box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
        .content-popup {
            height: 90px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .clearfix {
            margin-top: 25px;
            display: flex;
            justify-content: flex-end;
            button {
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                border: none;
                cursor: pointer;
                opacity: 0.9;
                text-align: right;
                border-radius: 5px;
                margin: 0px 5px 0px 5px;
                padding: 10px 15px 10px 15px;
            }
            button:hover {
                opacity: 1;
            }
            .cancelbtn,
            .deletebtn {
                float: left;
                height: 40px;
            }
            .cancelbtn {
                background-color: #ccc;
                color: black;
            }
            .deletebtn {
                background-color: #f44336;
            }
            .submitbtn {
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                border: none;
                cursor: pointer;
                opacity: 0.9;
                text-align: right;
                border-radius: 5px;
                margin: 0px 5px 0px 5px;
                padding: 10px 15px 10px 15px;
                background-color: #0088ff;
            }
            .submitbtn:hover {
                opacity: 1;
            }
        }
        .container {
            padding: 16px;
            text-align: center;
        }
        .clearfix::after {
            content: "";
            clear: both;
            display: table;
        }
    }
    .ui-widget {
        font-size: 20px;
    }
    .option-add-time {
        margin: 0;
        height: 45px;
        justify-content: center;
    }
}

.admin-booking {
    .row.option {
        overflow: hidden;
        max-height: none;
        margin: 0 0 35px 0;
    }
}

.modal-body p {
    white-space: nowrap;
}
