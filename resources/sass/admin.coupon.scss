@import "variables";
.manage-coupon {
    .btn-group {
        a.btn-ct-primary {
            width: 160px;
        }
    }
    .order-item-sort {
        margin-bottom: 25px;
    }
    .form-search-coupon {
        button {
            margin-left: 5px;
            padding: 5px 0;
            width: 100px;
        }
        .form-control {
            height: calc(0.6em + 1.25rem + 5px);
        }
        input {
            color: black;
            &::-webkit-input-placeholder {
                font-size: 12px;
            }
            &::-moz-placeholder {
                font-size: 12px;
            }
            &:-ms-input-placeholder {
                font-size: 12px;
            }
            &:-moz-placeholder {
                font-size: 12px;
            }
        }
    }
    .box-coupon {
        color: black;
        border: 1px solid $dark2;
        padding: 15px 20px 30px 20px;
        margin-top: 20px;
        box-shadow: 0px 4px 12px -4px rgb(0 0 0 / 19%);
        border-radius: 6px;
        p {
            margin-bottom: 0;
            font-size: 14px;
        }
        .table {
            margin-top: 10px;
            text-align: center;
            .table-bordered td,
            .table-bordered th {
                border: 1px solid black;
                font-size: 15px;
                color: black;
            }
        }
        .table-date {
            .left,
            .right {
                width: 50%;
                float: right;
            }
            .date {
                clear: both;
                display: flex;
                justify-content: space-evenly;
                font-size: 14px;
                word-spacing: 10px;
            }
        }
        .table-left {
            .table td,
            .table th {
                padding: 0.5rem 1rem;
            }
        }
        .group-button {
            margin-top: 30px;
            text-align: center;
            .button-default {
                width: 15rem;
                height: auto;
                padding: 15px 0;
                border: 1px solid $bgDeleted;
                color: $white;
            }
            .bg-edit {
                color: $black;
            }
        }
        .check-box-ct {
            display: flex;
            justify-content: center;
            span {
                margin-right: 50px;
            }
            input[type="checkbox"] {
                height: 18px;
                width: 18px;
                margin-top: 2px;
            }
            label {
                margin-left: 5px;
            }
        }
    }
    button.btn.button-search {
        width: 7em;
        padding: 0.25em;
        border: 2px solid $btnSave;
        background-color: $btnSave;
        margin-left: 5px;
        color: white;
        font-size: 0.875rem;
    }
}

@media (min-width: 767px) {
    .box-coupon {
        .table-right {
            max-height: 310px;
            overflow: hidden;
            &:hover,
            &:focus,
            &:active {
                overflow-y: auto;
            }
        }
    }
}

@media (max-width: 1024px) {
    .manage-coupon {
        .btn-group {
            a.btn-ct-primary {
                width: 143px;
            }
        }
    }
}

@media (max-width: 767px) {
    .manage-coupon {
        .box-coupon {
            .group-button {
                .button-default {
                    width: 11rem;
                }
            }
            .table-right {
                margin-left: -15px;
                .table td,
                .table th {
                    padding: 0.5rem 0.8rem;
                }
            }
        }
    }
}

select {
    height: 23px;
}

//page quick view tablet
.quick-view .box-coupon .table-right {
    overflow: auto;
}
