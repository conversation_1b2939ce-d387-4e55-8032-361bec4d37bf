@import "./variables";
.calendar-page {
    color: $black;
    button {
        outline: none;
    }
    h1 {
        color: $black;
    }
    .row.text {
        margin: 76px 0 0 4px;
        font-size: 10px;
    }
    .text {
        p {
            border-bottom: 4px solid $blueH;
            font-weight: inherit;
            margin: 0 auto;
            text-align: center;
        }
    }
    .hasDatepicker {
        width: 100%;
        margin-bottom: 30px;
        > .ui-datepicker {
            width: 100%;
        }
        .ui-datepicker {
            table {
                margin: auto;
            }
            .ui-datepicker-title {
                line-height: 1.8em;
                text-align: center;
                font-weight: initial;
                font-size: 26px;
                width: 150px;
                margin: auto;
            }
            .ui-datepicker-header {
                .ui-state-hover {
                    background-color: white;
                    cursor: pointer;
                    border: 0;
                }
                .ui-corner-all {
                    top: 10px;
                }
                .ui-datepicker-next {
                    right: calc(50% - 80px);
                }
                .ui-datepicker-prev {
                    left: calc(50% - 80px);
                }
            }
        }
    }
}

@media screen and (min-width: 480px) {
    .calendar-page {
        .text {
            p {
                width: 208px;
                font-size: 14px;
            }
        }
    }
}
@media screen and (min-width: 1024px) {
    .calendar-page {
        .text {
            p {
                width: 321px;
                font-size: 20px;
            }
        }
    }
}

.booking-btn-next {
    display: inline-block;
    button {
        width: 9em;
        height: 36px;
        padding: 0;
        border-radius: 100px;
        margin-right: 20px;
        background-color: $orangeYellow;
        border: 1px solid $orange1;
        color: $white;
        &:hover,
        &:focus {
            color: $white;
        }
        &:active {
            color: $white;
            background-color: $orangeYellowH;
        }
    }
}

