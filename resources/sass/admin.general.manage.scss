@import "variables";
.managegeneral {
    margin: 0px -15px 0px -15px;
    padding-left: 13px;
    .col-sm-2 {
        padding-right: 0;
        max-width: 15%;
    }
    .col-sm-10 {
        padding-left: 0;
        max-width: 85%;
    }
    .title-page {
        font-size: 20px;
        text-align: center;
        width: 100%;
    }
    .name-page {
        margin-left: 30px;
        margin-bottom: 10px;
    }
    .border-title {
        margin: 30px 0px 0px 0px;
        height: 5px;
        width: 100%;
        border-top: 2px solid $black;
    }
    .link-home {
        color: $violet;
         :hover {
            color: $blue3;
        }
    }
    .link-home:hover {
        color: $blue3;
    }
    .btn-manage,
    .btn-edit {
        width: 120px;
        padding: 7px;
        border: 1px solid $dark1;
        border-radius: 20px;
    }
    .btn-edit.disabled {
        background-color: $btnDisable;
    }
    .btn-manage2 {
        border: 2px solid $black;
        margin: 10px 0px 0px 10px;
        width: 120px;
        padding: 10px 0px 10px 0px;
        background-color: white;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .btn-manage3 {
        border-bottom: 1px dashed $black;
        margin: 10px 0px 0px 10px;
        width: 120px;
        background-color: $white;
        height: 50px;
        display: flex;
        justify-content: left;
        align-items: flex-end;
        color: $violet;
    }
    .btn-manage3:hover {
        color: $btColorHover;
    }
    .btn-header {
        display: flex;
    }
    .date {
        margin-top: 10px;
        .img-calendar {
            #search-calendar {
                display: flex;
                justify-content: flex-start;
                .date-choose {
                    display: flex;
                    align-items: center;
                    border-radius: 5px;
                    margin-right: 10px;
                    img.datepicker.img-datepicker {
                        height: 50px;
                    }
                }
            }
        }
    }
    .d-none {
        display: none !important;
    }
    .d-flex-space {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .total-price {
            display: flex;
            .line {
                width: 200px;
                margin: 0px 20px 0px 0px;
                display: flex;
                justify-content: space-between;
                border-bottom: 2px solid #000000;
                height: 28px;
                .line-price {
                    display: flex;
                    justify-content: space-between;
                    width: 93%;
                    .min-text {
                        font-size: 14px;
                        line-height: 1.7;
                    }
                }
                .price, .unit {
                    color: red;
                }
            }
        }
    }
    .note-shop {
        margin-top: 20px;
        .view-note-shop {
            display: flex;
            align-items: center;
            padding-bottom: 20px;
            border-bottom: 1px black solid;
            .note-content {
                width: 80%;
                height: 200px;
                border: 1px black solid;
                white-space: pre-wrap;
                padding: 10px;
                overflow: auto;
            }
            .btn-edit {
                width: 200px;
                height: 50px;
                margin-left: 10px;
            }
        }
        .edit-note-shop {
            padding-bottom: 20px;
            border-bottom: 1px black solid;
            #change-note-shop {
                display: flex;
                align-items: center;
                .option-note {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    #content-color {
                        width: 40px;
                        height: 40px;
                        border: none;
                        padding: 0;
                        background-color: white;
                    }
                }
                #shop-note {
                    width: 80%;
                    height: 200px;
                    resize: none;
                    padding: 10px;
                }
                .btn-save {
                    margin-top: 20px;
                    color: $white;
                    border: 2px solid $btnSave;
                    border-radius: 20px;
                    width: 200px;
                    background-color: $btnSave;
                    height: 50px;
                    text-align: center;
                    margin-left: 10px;
                }
                .btn-save.permission {
                    background-color: $gray;
                    border: none;
                }
                .btn-save:active {
                    background-color: $btColor;
                }
            }
        }
    }
    .col-note-cast {
        margin-top: 20px;
        min-width: 250px;
    }
    .view-note-cast {
        display: flex;
        align-items: center;
        justify-content: center;
        .note-content {
            width: 100%;
            white-space: pre-wrap;
            text-align: left;
            height: 100px;
            overflow: auto;
        }
        .btn-edit {
            width: 120px;
            height: 50px;
        }
    }
    .edit-note-cast {
        .change-note-cast {
            display: flex;
            align-items: center;
            justify-content: center;
            .cast-note {
                width: 100%;
                height: 100px;
                resize: none;
            }
        }
        .btn-save {
            color: $white;
            border: 2px solid $btnSave;
            border-radius: 20px;
            width: 120px;
            background-color: $btnSave;
            height: 50px;
            text-align: center;
        }
        .btn-save.permission {
            background-color: $gray;
            border: none;
        }
        .btn-save:active {
            background-color: $btColor;
        }
    }
    .data-manage {
        margin-top: 20px;
        .all-status-order {
            display: flex;
            margin-bottom: 20px;
            .status-order {
                display: flex;
                margin-right: 15px;
                .square {
                    width: 20px;
                    height: 20px;
                    border: 1px solid black;
                }
            }
        }
    }
    .no-happen-yet {
        background-color: #fede16;
    }
    .confirmed-phone {
        background-color: #17a79d;
    }
    .happening {
        background-color: #00acee;
    }
    .extension {
        background-color: #D26700;
    }
    .success {
        background-color: #00a550;
    }
    .ten-minute-before {
        background-color: #f6931d;
    }
    .table-order {
        padding-bottom: 30px !important;
        tbody tr td {
            width: 10%;
        }
    }
    .showtable {
        position: relative;
        width: 100%;
        .table thead th, tr th {
            text-align: center;
            vertical-align: middle;
            text-transform: capitalize;
        }
        .table-shadown {
            padding: 0;
            margin: 0;
        }
        .table tbody, .table thead {
            border-right: 2px solid $gray1;
        }
        .table tbody td {
            text-align: center;
        }
        .table td {
            padding: 0.5rem;
            vertical-align: middle;
        }
        .table th {
            padding: 2px 2px;
            vertical-align: middle;
        }
        .col-order {
            min-width: 120px;
        }
        .name-time-cast {
            background-color: #cd5aa2;
            color: white;
            .name-cast {
                font-size: 18px;
                border-bottom: 0.1px black dashed;
                margin: 10px 10px;
            }
            .time-cast {
                margin: 0px 10px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                div.time {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    div {
                        width: 20px;
                        height: 20px;
                        line-height: 1.4;
                        border-radius: 50%;
                    }
                    div.time-start {
                        background-color: #2baaf1;
                    }
                    div.time-end {
                        margin-left: 10px;
                        background-color: #e5ad5d;
                    }
                }
            }
        }
        .total-price-cast {
            display: flex;
            justify-content: space-around;
            font-size: 18px;
            span {
                padding: 10px;
            }
        }
        .title-note-cast {
            background-color: #a882ba;
            color: white;
        }
    }
}
