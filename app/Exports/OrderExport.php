<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class OrderExport implements
    FromArray,
    ShouldAutoSize,
    WithHeadings,
    WithTitle,
    WithEvents,
    WithColumnWidths,
    WithStyles
{
    private $data;
    public $arrColumn = [
        'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W',
        'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW',
    ];

    public function __construct($data = [])
    {
        $this->data = $data;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function array(): array
    {
        return $this->data['data'];
    }

    /**
     * create headings sheet
     */
    public function headings(): array
    {
        return $this->data["columnName"];
    }

    /**
     * create name sheet
     */
    public function title(): string
    {
        return  $this->data['sheetName'];
    }

    /**
     *
     * @return array
     */
    public function columnWidths(): array
    {
        $data = [
            'A' => 8,
            'B' => 8,
            'C' => 16,
            'D' => 16,
            'E' => 8,
        ];

        $arrColumn = $this->arrColumn;
        $number = count($this->data["columnName"]) - 1;
        while ($number >= 0) {
            if (!array_key_exists($arrColumn[$number], $data)) {
                $data[$arrColumn[$number]] = 15;
            }
            $number--;
        }
        return $data;
    }


    /**
     *
     * @return void
     */
    public function styles(Worksheet $sheet)
    {
        $countData = count($this->data["data"]) + 1;
        $data = [
            "A1:AF$countData" => [
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                ],
            ],
        ];

        return $data;
    }

    /**
     * set style sheet
     */
    public function registerEvents(): array
    {
        $arrColumn = $this->arrColumn;
        $countWith = count($this->data["columnName"]);
        $countData = count($this->data["data"]);
        $alphabet = $arrColumn[$countWith - 1];

        return [
            AfterSheet::class => function (AfterSheet $event)  use ($alphabet, $countData) {
                ++$countData;
                $event->sheet->getStyle("A1:{$alphabet}1")->applyFromArray([
                    'alignment' => [
                        'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                    ],
                    'fill' => [
                        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                        'color' => ['argb' => 'E7E6E6']
                    ]
                ]);
                $event->sheet->getStyle("A1:$alphabet$countData")->applyFromArray([
                    'borders' => array(
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                            'color' => ['argb' => '000000'],
                        ],
                    ),
                    'alignment' => [
                        'wrapText' => true,
                    ],
                ]);
            }
        ];
    }
}
