<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;

use App\Services\NotifyService;
use Illuminate\Support\Facades\Log;

class NotifyJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    private $message;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($message)
    {
        //
        $this->message = $message;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info("Queue Worker...");
        $msTeamNotify = env("MS_TEAM_NOTIFY", false);
        if ($msTeamNotify) {
            $response = Http::post($msTeamNotify, [
                'text' => $this->message,
            ]);
            Log::info("SEND RESPONSE:");
            Log::info($response);
        }
    }
}
