<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\OptionInformation;

class Option extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'option_info_id', 'name', 'price', 'parent', 'created_at', 'updated_at', 'deleted_at', 'ordinal_number',
    ];

    /**
     * Get the information that owns the option.
     */
    public function information()
    {
        return $this->belongsTo(OptionInformation::class, 'option_info_id');
    }
}
