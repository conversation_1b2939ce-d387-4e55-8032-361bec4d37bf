<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Carbon\Carbon;

class CustomerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'name' => 'required',
            'store_member_number' => 'nullable|regex:/^[0-9]{1,7}$/',
            'year_birth' => 'required',
            'month_birth' => 'required',
            'day_birth' => 'required',
        ];
        if ($this->id) {
            $rules['phone_number'] = "required|unique:customers,phone_number,$this->id,id,deleted_at,NULL";
            $rules['year_created'] = 'required';
            $rules['month_created'] = 'required';
            $rules['day_created'] = 'required';
        } else {
            $rules['phone_number'] = "required|unique:customers,phone_number,NULL,id,deleted_at,NULL";
        }

        return $rules;
    }

    public function messages()
    {
        return [
            'name.required' => "※お客様名を入力してください。",
            'store_member_number.regex' => "※この項目は数字のみ有効です。",
            'phone_number.required' => "※電話番号を入力してください。",
            'phone_number.unique' => "※既に、この電話番号で会員を登録されています。",
            'day_birth.required' => "※日を入力してください。",
            'day_created.required' => "※登録日を入力してください。",
        ];
    }
}
