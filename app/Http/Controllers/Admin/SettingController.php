<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\SettingRequest;
use App\Repositories\Setting\SettingRepository;

class SettingController extends Controller
{
    private $settingRepository;

    public function __construct(SettingRepository $settingRepository)
    {
        $this->settingRepository = $settingRepository;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $setting = $this->settingRepository->getSetting();
        if ($setting) {
            return view('admin.setting.index', [
                'setting' => $setting,
            ]);
        } else {
            return abort(404);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  App\Http\Requests\SettingRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function update(SettingRequest $request)
    {
        $settingUpdate = [
            "tax_percentage" => $request->tax_percentage,
            "minute_add" => $request->minute_add,
            "minute_extension" => $request->minute_extension,
            "price" => $request->price,
            "price_cast" => $request->price_cast,
            "interval" => $request->interval,
            "price_point" => $request->price_point,
            "point_price" => $request->point_price,
            "room_interval" => $request->room_interval,
            'updated_at' => now(),
            'price_minute_add' => $request->price_minute_add,
            'price_decorate_room_1' => $request->price_decorate_room_1,
            'price_decorate_room_2' => $request->price_decorate_room_2,
            'price_decorate_room_3' => $request->price_decorate_room_3
        ];
        $this->settingRepository->update($request->id, $settingUpdate);
        return redirect()->route('admin.setting.index')->with('popup2', 1);
    }
}
