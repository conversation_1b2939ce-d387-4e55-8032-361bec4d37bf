<?php

namespace App\Http\Controllers\Admin;

use App\Constants\CastCalendar as ConstantCastCalendar;
use App\Constants\Room as ConstantRoom;
use App\Http\Controllers\Controller;
use App\Http\Requests\CastCalendarRequest;
use App\Models\CastCalendar;
use App\Models\Order;
use App\Models\PriceDecorateRoom;
use App\Models\Room;
use App\Repositories\CastCalendar\CastCalendarRepository;
use App\Repositories\CastPriceLiving\CastPriceLivingRepository;
use App\Repositories\CastRepository;
use App\Repositories\Order\OrderRepository;
use App\Repositories\PriceDecorateRoom\PriceDecorateRoomRepository;
use App\Repositories\RoomRepository;
use App\Repositories\Room\RoomCalendarRepository;
use App\Repositories\Setting\SettingRepository;
use App\Repositories\SettingPriceLiving\SettingPriceLivingRepository;
use App\Repositories\SettingPriceSupport\SettingPriceSupportRepository;
use Exception;
use Facade\FlareClient\Http\Response;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CastCalendarController extends Controller
{
    private $castCalendarRepository;
    private $roomCalendarRepository;
    private $orderRepository;
    private $roomRepository;
    private $isTabletBooking;
    private $castRepository;
    private $settingRepository;
    private $settingPriceLivingRepository;
    private $settingPriceSupportRepository;
    private $castPriceLivingRepository;
    private $priceDecorateRoomRepository;

    public function __construct(
        RoomRepository $roomRepository,
        CastCalendarRepository $castCalendarRepository,
        RoomCalendarRepository $roomCalendarRepository,
        OrderRepository $orderRepository,
        CastRepository $castRepository,
        SettingRepository $settingRepository,
        SettingPriceLivingRepository $settingPriceLivingRepository,
        SettingPriceSupportRepository $settingPriceSupportRepository,
        CastPriceLivingRepository $castPriceLivingRepository,
        PriceDecorateRoomRepository $priceDecorateRoomRepository
    ) {
        $this->castCalendarRepository = $castCalendarRepository;
        $this->roomRepository = $roomRepository;
        $this->orderRepository = $orderRepository;
        $this->roomCalendarRepository = $roomCalendarRepository;
        $this->castRepository = $castRepository;
        $this->settingRepository = $settingRepository;
        $this->settingPriceLivingRepository = $settingPriceLivingRepository;
        $this->settingPriceSupportRepository = $settingPriceSupportRepository;
        $this->castPriceLivingRepository = $castPriceLivingRepository;
        $this->priceDecorateRoomRepository = $priceDecorateRoomRepository;
        $this->isTabletBooking = strpos(url()->current(), route('booking.index')) !== false;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

    public function index(Request $request)
    {
        $now = Carbon::now();
        $dateNow = clone $now;
        $dateShow = clone $dateNow;
        $isToday = true;
        if (isset($request->date) && $request->date) {
            $dateNow = Carbon::parse($request->date . ' ' . $dateNow->format('H:i'));
            $dateShow = clone $dateNow;
        } elseif ($dateShow->hour < CLOSE_HOURS - 24) {
            $dateShow->subDay();
            $isToday = false;
        }
        if ($dateNow->format('Y-m-d') != $now->format('Y-m-d')) {
            $dateNow = Carbon::parse($dateNow->format('Y-m-d'));
            $dateShow = clone $dateNow;
            $dateNow->addHours(CLOSE_HOURS);
            $isToday = false;
        }

        $filters = $request->all();
        if (isset($filters['status'])) {
            if ($filters['status'] == CastCalendar::STATUS_PREPARE) {
                $filters['arr_status'] = [
                    CastCalendar::STATUS_PREPARE,
                    CastCalendar::STATUS_WORKING,
                    CastCalendar::STATUS_FINISH,
                ];
            } else {
                $filters['arr_status'] = [
                    CastCalendar::STATUS_ABSENT,
                ];
            }
        }

        $this->data['dateShow'] = $dateShow;
        $this->data['castCalendarsNotRegister'] = [];
        $this->data['filters'] = $request->all();
        $this->data['castCalendars'] = $this->castCalendarRepository->getCastCalendarSortStatus($dateNow, $filters, $isToday);
        $this->data['castRoomCalendars'] = $this->roomCalendarRepository->getListNameRoomCastByDate($dateNow, $isToday)->keyBy('cast_id')->toArray();
        $this->data['countCastWorking'] = $this->castCalendarRepository->getCastWorking($dateShow)->count();
        $this->data['countCastPrepare'] = $this->castCalendarRepository->getCastPrepare($dateShow)->count();

        if (!isset($filters['status']) || $filters['status'] == CastCalendar::STATUS_ABSENT) {
            $this->data['castCalendarsNotRegister'] = $this->castCalendarRepository->getCastCalendarAbsentNotRegister($dateNow, $filters, $isToday);
        }

        if ($this->isTabletBooking) {
            return $this->renderView('frontend.quick_view_admin.cast_calendar');
        }
        return $this->renderView('admin.cast.calendar.index');
    }

    /**
     * update actual date start and date end
     *
     * @param  Request $request
     * @return \Illuminate\Http\Response
     */
    public function updateTime(Request $request)
    {
        $arrType = [
            ConstantCastCalendar::UPDATED_TIME_START,
            ConstantCastCalendar::UPDATED_TIME_END,
            ConstantCastCalendar::UPDATED_TIME_BACK_START,
            ConstantCastCalendar::UPDATED_TIME_BACK_END
        ];
        $castCalendarUpdate = $this->castCalendarRepository->find($request->id);
        if (!$request->type || !in_array($request->type, $arrType) || !$castCalendarUpdate) {
            return redirect()->back()->with('alert-errors', 'データが適切ではありません。');
        }
        $cast = $this->castRepository->find($castCalendarUpdate->cast_id);
        $now = Carbon::now()->format('Y-m-d H:i');
        if ($request->type == ConstantCastCalendar::UPDATED_TIME_START) {
            if (!$castCalendarUpdate->actual_date_start) {
                $castCalendarUpdate->actual_date_start = $now;
                $castCalendarUpdate->dormitory_price += $cast->dormitory_price;
            }
        } elseif ($request->type == ConstantCastCalendar::UPDATED_TIME_END) {
            if (!$castCalendarUpdate->actual_date_end && $castCalendarUpdate->actual_date_start) {
                $castCalendarUpdate->actual_date_end = $now;
            }
        } elseif ($request->type == ConstantCastCalendar::UPDATED_TIME_BACK_START) {
            if ($castCalendarUpdate->actual_date_start && !$castCalendarUpdate->actual_date_end) {
                $castCalendarUpdate->actual_date_start = null;
                if ($castCalendarUpdate->dormitory_price >= $cast->dormitory_price) {
                    $castCalendarUpdate->dormitory_price -= $cast->dormitory_price;
                } else {
                    $castCalendarUpdate->dormitory_price = 0;
                }
            }
        } elseif ($request->type == ConstantCastCalendar::UPDATED_TIME_BACK_END) {
            if ($castCalendarUpdate->actual_date_end) {
                $castCalendarUpdate->actual_date_end = null;
                $castCalendarUpdate->total_price_pay = 0;
                $castCalendarUpdate->is_pay = CastCalendar::UNPAID;
            }
        }
        $castCalendarUpdate->save();

        $filters = $request->filters;
        $dateNow = Carbon::now();
        $isToday = true;
        if ($dateNow->hour < CLOSE_HOURS - 24) {
            $isToday = false;
        }
        if (isset($filters['date'])) {
            $dateNow = Carbon::parse($filters['date'] . ' ' . $dateNow->format('H:i'));
        }
        $filters['cast_calendar_id'] = $request->id;

        $castCalendar = $this->castCalendarRepository->getCastCalendarSortStatus($dateNow, $filters, $isToday);
        return Response()->json([
            'success' => 1,
            'data' => $castCalendar->first()
        ]);
    }

    public function getRoom(Request $request)
    {
        $listRooms = $this->roomRepository->getListRoom($request->get('type'));

        return Response()->json([
            "listRooms" => $listRooms,
        ]);
    }

    /**
     * edit
     *
     * @param  int $id
     * @return view
     */
    public function edit($id, $date)
    {
        $dateNow = Carbon::now();
        $castCalendar = $this->castCalendarRepository->getCastCalendar($id);
        $castCalendar['absent'] = 1;
        $castCalendar['delete'] = 1;
        $orders = $this->orderRepository->getOrderOfCast($castCalendar->cast_id, $date, true);
        if (count($orders)) {
            foreach ($orders as $order) {
                if ($order->status == Order::STATUS_HAPPENING || $order->status == Order::STATUS_EXTENSION) {
                    $castCalendar['absent'] = 0;
                }
                if ($order->status != Order::STATUS_CANCEL) {
                    $order->start_time = Carbon::parse($order->date_start)->format('H:i');
                    $order->end_time = Carbon::parse($order->date_end)->format('H:i');
                    if ($order->actual_date_start) {
                        $order->start_time = Carbon::parse($order->actual_date_start)->format('H:i');
                    }
                    if ($order->actual_date_end) {
                        $order->end_time = Carbon::parse($order->actual_date_end)->format('H:i');
                    }
                    $timeStart = explode(":", $order->start_time);
                    $timeEnd = explode(":", $order->end_time);
                    if ($timeStart[0] < CLOSE_HOURS - 24) {
                        $timeStart[0] += 24;
                    }
                    if ($timeEnd[0] < CLOSE_HOURS - 24) {
                        $timeEnd[0] += 24;
                    }
                    $order->start_time = implode(":", $timeStart);
                    $order->end_time = implode(":", $timeEnd);
                }
                if ($order->support_id) {
                    $order->support = $this->settingPriceSupportRepository->findWithTrashed($order->support_id);
                }
            }
        }
        if ($castCalendar->type == CastCalendar::TYPE_ABSENT || $castCalendar->actual_date_start || count($orders)) {
            $castCalendar['delete'] = 0;
        }
        $dayWorkInMonth = $this->castCalendarRepository->getDayWorkInMonth($castCalendar->cast_id);
        $lastDaysWorkInBeforeMonth = $this->castCalendarRepository->getLastDayWorkInBeforeMonths($castCalendar->cast_id);
        $dateStart = Carbon::parse($castCalendar->date_start);
        $dateEnd = Carbon::parse($castCalendar->date_end);
        $listItemPriceLiving = $this->settingPriceLivingRepository->getAll();
        $listItemPriceSupport = $this->settingPriceSupportRepository->getAll();
        $listCastPriceLiving = $this->castPriceLivingRepository->getListByCastlendar($castCalendar->cast_id, $castCalendar->date);
        foreach ($listCastPriceLiving as $item) {
            $item->itemLiving = $this->settingPriceLivingRepository->findWithTrashed($item->item_id);
        }
        $priceDecorateRoomPaid = $this->priceDecorateRoomRepository->getPriceOfMonth($castCalendar->cast_id, Carbon::parse($castCalendar->date));
        $dayOfDecorateRoom = $lastDaysWorkInBeforeMonth > PriceDecorateRoom::NUMBER_DAYS_MOVE_NEXT_MONTH ? $dayWorkInMonth : ($dayWorkInMonth + $lastDaysWorkInBeforeMonth);
        $totalPriceDecorateRoom = $this->settingRepository->getPriceDecorateRoomByDays($dayOfDecorateRoom);
        $priceDecorateRoom = $this->settingRepository->getPriceDecorateRoomByDays($dayWorkInMonth);

        return view('admin.cast.calendar.edit', [
            'castCalendar' => $castCalendar,
            'dayWorkInMonth' => $dayWorkInMonth,
            'lastDaysWorkInBeforeMonth' => $lastDaysWorkInBeforeMonth,
            'dateNow' => $dateNow,
            'dateStart' => $dateStart,
            'dateEnd' => $dateEnd,
            'date' => $date,
            'listItemPriceLiving' => $listItemPriceLiving,
            'listItemPriceSupport' => $listItemPriceSupport,
            'orders' => $orders,
            'listCastPriceLiving' => $listCastPriceLiving,
            'priceDecorateRoom' => $priceDecorateRoom,
            'totalPriceDecorateRoom' => $totalPriceDecorateRoom,
            'priceDecorateRoomPaid' => $priceDecorateRoomPaid
        ]);
    }

    /**
     * update
     *
     * @param  object $request
     * @param  int $id
     * @return view
     */
    public function update(CastCalendarRequest $request)
    {
        $castCalendar = $this->castCalendarRepository->getCastCalendar($request->id);
        if (!$castCalendar) {
            return redirect()->back()->with('alert-errors', 'データが適切ではありません。');
        }
        if ($request->delete) {
            DB::beginTransaction();
            try {
                $orders = $this->orderRepository->getOrderByCastId($request->cast_id, $request->date);
                $castCalendar = $this->castCalendarRepository->getCastCalendar($request->id);
                if ($castCalendar->actual_date_start || count($orders)) {
                    return redirect()->back()->with('alert-errors', 'データが適切ではありません。');
                }
                $data['is_room_change'] = NULL;
                $castCalendar->date_start = null;
                $castCalendar->date_end = null;
                $castCalendar->actual_date_start = NULL;
                $castCalendar->actual_date_end = NULL;
                $castCalendar->type = CastCalendar::TYPE_ABSENT;
                $castCalendar->save();
                $this->roomCalendarRepository->deleteRoomCaledarByCast($request->cast_id, $request->date);
                if (!$castCalendar->is_pay) {
                    $this->updateOptionCalendar($request, $castCalendar);
                }
                DB::commit();
            } catch (Exception $e) {
                DB::rollBack();

                return redirect()->back()->with('alert-errors', 'データが適切ではありません。');
            }
        } else {
            if ($request->day_off) {
                DB::beginTransaction();
                try {
                    $orders = $this->orderRepository->getOrderByCastId($request->cast_id, $request->date);
                    if (count($orders)) {
                        foreach ($orders as $order) {
                            if ($order->status == Order::STATUS_HAPPENING || $order->status == Order::STATUS_EXTENSION) {
                                return redirect()->back()->with('alert-errors', '発進中の予約がある為、休日に指定できません。');
                            }
                            if ($order->status == Order::STATUS_NO_HAPPEN_YET) {
                                $order->status = Order::STATUS_CANCEL;
                                $order->save();
                                if ($order->is_pay == Order::PAID) {
                                    $castCalendar->price_cast -= $order->price_cast;
                                }
                            }
                        }
                    }
                    $castCalendar->date_start = null;
                    $castCalendar->date_end = null;
                    $castCalendar->actual_date_start = NULL;
                    $castCalendar->actual_date_end = NULL;
                    $castCalendar->room_id = NULL;
                    $castCalendar->is_room_change = NULL;
                    $castCalendar->type = ConstantCastCalendar::TYPE_OFF;
                    $this->roomCalendarRepository->deleteRoomCaledarByCast($request->cast_id, $request->date);

                    $castCalendar->save();
                    if (!$castCalendar->is_pay) {
                        $this->updateOptionCalendar($request, $castCalendar);
                    }
                    DB::commit();
                } catch (Exception $e) {
                    DB::rollBack();

                    return redirect()->back()->with('alert-errors', 'データが適切ではありません。');
                }
            } else {
                DB::beginTransaction();
                try {
                    $inforRoomBook = $this->roomCalendarRepository->getInforBookRoomOfCast($request->cast_id, $request->date);
                    $hourStart = $request->hour_start;
                    $minuteStart = $request->minute_start;
                    $hourEnd = $request->hour_end;
                    $minuteEnd = $request->minute_end;
                    $castCalendar->type = CastCalendar::TYPE_WORKING;
                    if ($request->share_room) {
                        $castCalendar->is_room_change = CastCalendar::SHARE_ROOM;
                        if ($inforRoomBook && $inforRoomBook->room->type == Room::SHARE_ROOM) {
                            $castCalendar->room_id = $inforRoomBook->room_id;
                        } else {
                            $this->roomCalendarRepository->deleteRoomCaledarByCast($request->cast_id, $request->date);
                            $castCalendar->room_id = null;
                        }
                    } else {
                        $castCalendar->is_room_change = CastCalendar::NO_SHARE_ROOM;
                        if ($inforRoomBook && $inforRoomBook->room->type == Room::NO_SHARE_ROOM) {
                            $castCalendar->room_id = $inforRoomBook->room_id;
                            $castCalendar->room_id = null;
                        } else {
                            $this->roomCalendarRepository->deleteRoomCaledarByCast($request->cast_id, $request->date);
                        }
                    }
                    $castCalendar->date_start = Carbon::parse($request->date)->addHours((int) $hourStart)->addMinutes((int) $minuteStart);
                    $castCalendar->date_end = Carbon::parse($request->date)->addHours((int) $hourEnd)->addMinutes((int) $minuteEnd);
                    if ($castCalendar->actual_date_start) {
                        $castCalendar->actual_date_start = Carbon::parse($request->date)->addHours((int) $hourStart)->addMinutes((int) $minuteStart)->format('Y-m-d H:i:s');
                    }
                    if ($castCalendar->actual_date_end) {
                        $castCalendar->actual_date_end = Carbon::parse($request->date)->addHours((int) $hourEnd)->addMinutes((int) $minuteEnd)->format('Y-m-d H:i:s');
                    }
                    // calculator price cast Order
					$orders = $this->orderRepository->getOrderOfCast($request->cast_id, $request->date, true);
					$priceCast = 0;
                    if (count($orders)) {
                        foreach ($orders as $order){
                            $priceCast += $order->price_cast;
                        }
                    }
					$castCalendar->price_cast = $priceCast;
                    $castCalendar->save();
                    if (!$castCalendar->is_pay) {
                        $this->updateOptionCalendar($request, $castCalendar);
                    }
                    DB::commit();
                } catch (Exception $e) {
                    DB::rollBack();

                    return redirect()->back()->with('alert-errors', 'データが適切ではありません。');
                }
            }
        }
        return redirect()->back()->with('alert-errors', '編集完了しました。');
    }

    public function updateOptionCalendar($request, $castCalendar)
    {
        $castCalendar->dormitory_price = $request->dormitory_price;
        $castCalendar->expenses = $request->expenses;
        $listIds = array();
        if ($request->option) {
            foreach ($request->option as $id => $option) {
                if ($option && $request->quantity[$id]) {
                    $priceCastLiving = $this->castPriceLivingRepository->find($id);
                    $priceCastLiving->item_id = $option;
                    $priceCastLiving->quantity = $request->quantity[$id];
                    $priceCastLiving->updated_by = Auth::user()->id;
                    $priceCastLiving->save();
                    $listIds[] = $priceCastLiving->id;
                } else {
                    $this->castPriceLivingRepository->delete($id);
                }
            }
        }
        $this->castPriceLivingRepository->deleteNotInIds($listIds, $castCalendar->cast_id, $castCalendar->date);
        if ($request->name_new) {
            foreach ($request->name_new as $key => $name) {
                if ($name && $request->quantity_new[$key]) {
                    $priceCastLiving = array();
                    $priceCastLiving = [
                        'cast_id' => $castCalendar->cast_id,
                        'date' => $castCalendar->date,
                        'item_id' => $name,
                        'price' => $this->settingPriceLivingRepository->find($name)->price,
                        'quantity' => $request->quantity_new[$key],
                        'created_by' => Auth::user()->id
                    ];
                    $this->castPriceLivingRepository->create($priceCastLiving);
                }
            }
        }
        if ($request->price_support) {
            foreach ($request->price_support as $orderId => $priceSupport) {
                if ($priceSupport) {
                    $order = $this->orderRepository->find($orderId);
                    $order->support_id = $priceSupport;
                    $order->price_cast += $this->settingPriceSupportRepository->find($priceSupport)->price - $order->support_price;
                    if ($order->is_pay) {
                        $castCalendar->price_cast += $this->settingPriceSupportRepository->find($priceSupport)->price - $order->support_price;
                    }
                    $order->support_price = $this->settingPriceSupportRepository->find($priceSupport)->price;
                } else {
                    $order = $this->orderRepository->find($orderId);
                    $order->support_id = null;
                    $order->price_cast -= $order->support_price;
                    if ($order->is_pay) {
                        $castCalendar->price_cast -= $order->support_price;
                    }
                    $order->support_price = 0;
                }
                $order->save();
            }
        }
        $castCalendar->save();
    }

    public function create($id, $date)
    {
        $dateNow = Carbon::now();
        $cast = $this->castRepository->find($id);
        if (!$cast) {
            return abort(404);
        }
        $dayWorkInMonth = $this->castCalendarRepository->getDayWorkInMonth($id);
        $priceOfCalendar = 0;
        $lastDaysWorkInBeforeMonth = $this->castCalendarRepository->getLastDayWorkInBeforeMonths($id);
        $priceDecorateRoomPaid = $this->priceDecorateRoomRepository->getPriceOfMonth($id, Carbon::parse($date));
        $dayOfDecorateRoom = $lastDaysWorkInBeforeMonth > PriceDecorateRoom::NUMBER_DAYS_MOVE_NEXT_MONTH ? $dayWorkInMonth : ($dayWorkInMonth + $lastDaysWorkInBeforeMonth);
        $totalPriceDecorateRoom = $this->settingRepository->getPriceDecorateRoomByDays($dayOfDecorateRoom);
        $priceDecorateRoom = $this->settingRepository->getPriceDecorateRoomByDays($dayWorkInMonth);

        return view('admin.cast.calendar.create', [
            'cast' => $cast,
            'dayWorkInMonth' => $dayWorkInMonth,
            'lastDaysWorkInBeforeMonth' => $lastDaysWorkInBeforeMonth,
            'priceOfCalendar' => $priceOfCalendar,
            'dateNow' => $dateNow,
            'date' => $date,
            'priceDecorateRoom' => $priceDecorateRoom,
            'totalPriceDecorateRoom' => $totalPriceDecorateRoom,
            'priceDecorateRoomPaid' => $priceDecorateRoomPaid
        ]);
    }

    public function store(Request $request)
    {
        $castCalendar = [
            'cast_id' => $request->cast_id,
            'type' => $request->has('day_off') ? CastCalendar::TYPE_ABSENT : CastCalendar::TYPE_WORKING,
            'date' => $request->date,
            'price_cast' => 0,
            'dormitory_price' => $request->dormitory_price,
            'expenses' => $request->expenses,
            'created_by' => auth()->id()
        ];
        if ($castCalendar['type'] == CastCalendar::TYPE_WORKING) {
            $dateStart = Carbon::parse($request->date)->addHours((int) $request->hour_start)->addMinutes((int) $request->minute_start);
            $dateEnd = Carbon::parse($request->date)->addHours((int) $request->hour_end)->addMinutes((int) $request->minute_end);
            $castCalendar['date_start'] = $dateStart;
            $castCalendar['date_end'] = $dateEnd;
            $castCalendar['is_room_change'] = $request->has('is_room_change') ? CastCalendar::SHARE_ROOM : CastCalendar::NO_SHARE_ROOM;
        }
        $this->castCalendarRepository->create($castCalendar);
        return redirect()->route('admin.cast_calendar.list', ['date' => $request->date]);
    }

    public function payPriceCast(Request $request)
    {
        $castCalendar = $this->castCalendarRepository->find($request->id);

        if (!$castCalendar || ($request->type != 'pay' && $request->type != 'back') || $request->totalPriceCast < 0) {
            return response()->json(['success' => 0]);
        }
        if ($request->type == 'pay') {
            if ($castCalendar->actual_date_end) {
                $castCalendar->is_pay = CastCalendar::PAID;
                $castCalendar->total_price_pay = $request->totalPriceCast;
                $castCalendar->save();
                return response()->json(['success' => 1]);
            }
            return response()->json(['success' => 0, 'msg' => 'キャストさんはまだ出勤中ので、支払いができません。']);
        }
        $castCalendar->total_price_pay = 0;
        $castCalendar->is_pay = CastCalendar::UNPAID;
        $castCalendar->save();
        return response()->json(['success' => 1]);
    }

    public function paypriceDecorateRoom(Request $request)
    {
        if ($request->date) {
            $cast = $this->castRepository->find($request->castId);

            if (!$cast || ($request->type != 'pay' && $request->type != 'back') || !$request->price) {
                return response()->json(['success' => 0]);
            }
            $date = Carbon::parse($request->date);
            $startOfMonth = Carbon::parse($request->date)->startOfMonth();
            $castId = $request->castId;
        } else {
            $castCalendar = $this->castCalendarRepository->find($request->id);

            if (!$castCalendar || ($request->type != 'pay' && $request->type != 'back') || !$request->price) {
                return response()->json(['success' => 0]);
            }
            $date = Carbon::parse($castCalendar->date);
            $startOfMonth = Carbon::parse($castCalendar->date)->startOfMonth();
            $castId = $castCalendar->cast_id;
        }
        $objectOld = $this->priceDecorateRoomRepository->getPriceOfMonth($castId, $date);
        if ($request->type == 'pay') {
            if ($objectOld) {
                return response()->json(['success' => 0]);
            }
            $object = [
                'cast_id' => $castId,
                'price' => $request->price,
                'date_pay' => $date->toDateString(),
                'date' => $startOfMonth->toDateString()
            ];
            $this->priceDecorateRoomRepository->create($object);
        } else {
            $objectOld->delete();
        }
        return response()->json(['success' => 1, 'date' => $date->toDateString()]);
    }

    public function checkPayTotalPrice(Request $request)
    {
        $castCalendar = $this->castCalendarRepository->find($request->id);

        if (!$castCalendar) {
            return response()->json(['success' => 0]);
        }

        return response()->json(['check' => $castCalendar->is_pay]);
    }
}
