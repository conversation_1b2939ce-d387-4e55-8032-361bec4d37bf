<?php

namespace App\Http\Controllers\Booking;

use App\Constants\Booking as ConstantBooking;
use App\Constants\Orders as ConstantOrders;
use App\Http\Controllers\Controller;
use App\Http\Requests\SearchPhoneRequest;
use App\Models\Coupon;
use App\Repositories\Coupon\CouponRepository;
use App\Repositories\Customer\CustomerRepository;
use App\Repositories\Option\OptionRepository;
use App\Repositories\Order\OrderRepository;
use App\Repositories\Setting\SettingRepository;
use App\Services\OrderService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Session;

class BookingPayController extends Controller
{
    private $customerRepository;
    private $optionRepository;
    private $orderRepository;
    private $orderService;
    private $settingRepository;
    private $couponRepository;

    public function __construct(
        CouponRepository $couponRepository,
        CustomerRepository $customerRepository,
        OptionRepository $optionRepository,
        OrderRepository $orderRepository,
        OrderService $orderService,
        SettingRepository $settingRepository
    ) {
        $this->couponRepository = $couponRepository;
        $this->customerRepository = $customerRepository;
        $this->optionRepository = $optionRepository;
        $this->orderRepository = $orderRepository;
        $this->orderService = $orderService;
        $this->settingRepository = $settingRepository;
    }

    public function check()
    {
        Session::forget(ConstantBooking::SESSION_BOOKING_TYPE);
        $this->orderService->forget();

        return view('frontend.booking_direct.check');
    }

    public function phone()
    {
        Session::put(ConstantBooking::SESSION_BOOKING_TYPE, ConstantBooking::WITH_RESERVATION);

        return view('frontend.booking_direct.phone');
    }

    /**
     *
     * @param  SearchPhoneRequest $request
     * @return \Illuminate\Http\Response
     */
    public function searchPhone(SearchPhoneRequest $request)
    {
        Session::put(ConstantBooking::SESSION_BOOKING_TYPE, ConstantBooking::WITH_RESERVATION);
        $phone = preg_replace('/\s+|-/', '', $request->phone);
        Session::put(ConstantBooking::SESSION_BOOKING_SEARCH_PHONE, $phone);
        return redirect()->route('booking.show.orders');
    }

    public function showOrders()
    {
        $phone = Session::get(ConstantBooking::SESSION_BOOKING_SEARCH_PHONE);
        if (!$phone) return redirect()->route('booking.phone');
        $orders = $this->orderRepository->getUnpaidOrderByPhone($phone);
        if (!count($orders)) return redirect()->route('booking.phone');
        foreach ($orders as $order) {
            $dateStart = Carbon::parse($order->date_start);
            $this->data['dataOrders'][$order->id] = $order;
            $this->data['dataCouponNormals'][$order->id] = $this->couponRepository->getListCouponByCoursesCast($order->course_id, $order->cast_id, $dateStart, Coupon::TYPE_COUPON);
            $this->data['dataCouponTickets'][$order->id] = $this->couponRepository->getListCouponByCoursesCast($order->course_id, $order->cast_id, $dateStart,  Coupon::TYPE_COUPON_TICKET);
            $this->data['dataCouponMember'][$order->id] = $this->couponRepository->getListCouponByCoursesCast($order->course_id, $order->cast_id, $dateStart,  Coupon::TYPE_COUPON_MEMBER);
            $this->data['dataOptions'][$order->id] = $this->optionRepository->getArrOptionByCast($order->cast_id, $dateStart->format('Y-m-d'));
            $this->data['dataCustomer'][$order->id] = $this->customerRepository->getMember($order->phone_point, $order->name);
            $order->textMethod = $this->orderService->getTextMethod($order->really_method);
        }

        $this->data['setting'] = $this->settingRepository->getSetting();
        return $this->renderView('frontend.booking_direct.show_order');
    }

    /**
     * update total price, option, coupon
     *
     * @param  Request $request
     * @return \Illuminate\Http\Response
     */
    public function updateOrder(Request $request)
    {
        if (!$request->input_check) return redirect()->back()->withErrors('Not check item');
        $phone = Session::get(ConstantBooking::SESSION_BOOKING_SEARCH_PHONE);
        $orders = $this->orderRepository->getUnpaidOrderByPhone($phone);

        foreach ($orders as $key => $item) {
            if (!in_array($item->id, $request->input_check)) unset($orders[$key]);
        }
        $sessionOrderId = [];
        $number = '';
        $totalPrice = 0;
        $defaultTypePay = ConstantOrders::TYPE_PAYMENT_CASH;
        foreach ($request->input_check as $orderId) {
            $couponIds = [$request->{'coupon_normal_' . $orderId}, $request->{'coupon_ticket_' . $orderId}, $request->{'coupon_member_' . $orderId}];
            $couponIds = Arr::where($couponIds, function ($value, $key) {
                return $value > 0 && $value != null;
            });
            $optionIds = [$request->{'option_parent_' . $orderId}, $request->{'option_child_' . $orderId}];
            $optionIds = Arr::where($optionIds, function ($value, $key) {
                return $value > 0 && $value != null;
            });
            $dataUpdate = [
                'coupon_ids' =>  $couponIds,
                'option_ids' =>  $optionIds,
                'phone_number' =>  $request->{'phone_number_' . $orderId},
                'point_use' =>  $request->{'point_use_' . $orderId},
            ];

            $result = $this->orderService->updateOrder($orderId, $dataUpdate);
            if ($result['success']) {
                $order = $result['order'];
                $sessionOrderId[] = $order->id;
                $number .= $order->number . ',';
                if ($order->type_pay == ConstantOrders::TYPE_PAYMENT_CARD) {
                    $totalPrice += $this->orderService->getPriceNotTaxPercentage($order->total_price);
                } else {
                    $totalPrice += $order->total_price;
                }
                if (count($request->input_check) == 1) {
                    $defaultTypePay = $order->type_pay;
                }
            } else {
                return redirect()->back()->withErrors($result['message']);
            }
        }
        Session::put(ConstantBooking::SESSION_BOOKING_ORDER_PAY, [
            'order_ids' => $sessionOrderId,
            'order_numbers' => trim($number, ','),
            'total_price' => $totalPrice,
            'defaultTypePay' => $defaultTypePay
        ]);
        return redirect()->route('booking.pay.order');
    }

    /**
     * view page order pay
     *
     * @return \Illuminate\Http\Response
     */
    public  function payOrder()
    {
        $orderInfo = Session::get(ConstantBooking::SESSION_BOOKING_ORDER_PAY);
        $this->data['number'] = $orderInfo['order_numbers'];
        $this->data['totalPrice'] = $orderInfo['total_price'];
        $this->data['totalPercent'] = $this->orderService->getPriceTaxPercentage($orderInfo['total_price']);
        $this->data['defaultTypePay'] = $orderInfo['defaultTypePay'];
        if ($this->data['totalPrice'] < 0 || !$this->data['number']) {
            return redirect()->back()->withErrors('errors');
        }
        return $this->renderView('frontend.booking_direct.payment');
    }

    /**
     * payment orders
     *
     * @param  Request $request
     * @return json
     */
    public function storePayOrder(Request $request)
    {
        $type = ConstantOrders::TYPE_PAYMENT_CARD;
        if ($request->type_payment == ConstantOrders::TYPE_PAYMENT_CASH) {
            $type = ConstantOrders::TYPE_PAYMENT_CASH;
        }
        $orderInfo = Session::get(ConstantBooking::SESSION_BOOKING_ORDER_PAY);
        $result = $this->orderService->payOrder($orderInfo['order_ids'], $type);
        if (!$result['success']) {
            return Response()->json([
                'success' => 0,
                'message' => $result['message']
            ]);
        }
        return Response()->json([
            'success' => 1,
        ]);
    }
}
