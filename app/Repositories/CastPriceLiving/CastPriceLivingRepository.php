<?php

namespace App\Repositories\CastPriceLiving;

use App\Models\CastCalendar;
use App\Models\CastPriceLiving;
use App\Repositories\BaseRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;

class CastPriceLivingRepository extends BaseRepository implements CastPriceLivingRepositoryInterface
{
    public function getModel()
    {
        return CastPriceLiving::class;
    }

    public function getListByCastlendar($castId, $date)
    {
        $listItem = $this->model->where('cast_id', $castId)
            ->where('date', $date)
            ->get();

        return $listItem;
    }

    public function getPriceItemByCastCalendar($castId, $date)
    {
        $price = $this->model->selectRaw('sum(price * quantity) as total_price')->where('cast_id', $castId)
            ->where('date', $date)
            ->first()->total_price;

        return $price;
    }

    public function updatePrice($itemId, $price)
    {
        $now = Carbon::now();
        if ($now->hour < CLOSE_HOURS - 24) {
            $now->subDay();
        }
        $price = $this->model->where('date', $now->toDateString())
            ->where('item_id', $itemId)
            ->update(['price' => $price]);

        return true;
    }

    public function deleteNotInIds($listIds, $castId, $date)
    {
        $this->model->whereNotIn('id', $listIds)->where('cast_id', $castId)
            ->where('date', $date)
            ->delete();

        return true;
    }

    public function totalPriceByTime($dateStart, $dateEnd)
    {
        $tblCastPriceLiving = with(new CastPriceLiving())->getTable();
        $tblCastCalendar = with(new CastCalendar())->getTable();

        return $this->model->selectRaw('sum(price*quantity) as total')
            ->joinSub(DB::table($tblCastCalendar)
                ->select(
                    "$tblCastCalendar.is_pay as is_pay_calendar",
                    "$tblCastCalendar.cast_id",
                    "$tblCastCalendar.date"
                )
                ->whereNull("$tblCastCalendar.deleted_at"), 'cast_calendar_sub', function ($join) use ($tblCastPriceLiving) {
                $join->on("$tblCastPriceLiving.cast_id", '=', "cast_calendar_sub.cast_id");
                $join->on("$tblCastPriceLiving.date", '=', "cast_calendar_sub.date");
            })
            ->where('is_pay_calendar', CastCalendar::PAID)
            ->where("$tblCastPriceLiving.date", '>=', $dateStart)
            ->where("$tblCastPriceLiving.date", '<=', $dateEnd)
            ->first()
            ->total;
    }
}
