<?php

namespace App\Repositories\Customer;

use App\Repositories\RepositoryInterface;

interface CustomerRepositoryInterface extends RepositoryInterface
{
    function getModel();

    /**
     * findCustomerOfPhone
     *
     * @param  mixed $phone
     * @return object
     */
    public function findCustomerOfPhone($phone);

    /**
     * getLastUpdateDate
     *
     * @return object
     */
    public function getLastUpdateDate();

    /**
     * getMembers
     *
     * @return object
     */
    public function getMembers();

    /**
     *
     * @param  int $phone
     * @param  string $name
     * @return collection
     */
    public function getMember($phone, $name);

    /**
     *
     * @return int
     */
    public function getMaxId();
}
