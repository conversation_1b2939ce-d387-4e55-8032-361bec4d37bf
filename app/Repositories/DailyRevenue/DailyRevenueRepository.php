<?php

namespace App\Repositories\DailyRevenue;

use App\Models\DailyRevenue;
use App\Repositories\BaseRepository;
use Carbon\Carbon;

class DailyRevenueRepository extends BaseRepository implements DailyRevenueRepositoryInterface
{
    public function getModel()
    {
        return DailyRevenue::class;
    }

    public function findByDate($date)
    {
        if ($date instanceof Carbon) {
            $date = $date->toDateString();
        }
        return $this->model->whereDate('date', $date)->first();
    }

    public function getFirst15DaysStatistics($date)
    {
        if (!($date instanceof Carbon)) {
            $date = Carbon::parse($date);
        }

        $startOfMonth = $date->copy()->startOfMonth();
        $day15 = $date->copy()->startOfMonth()->addDays(14); // Day 1-15

        return $this->model
            ->selectRaw('SUM(card_orders_count) as total_orders')
            ->selectRaw('SUM(card_orders_total) as total_price')
            ->selectRaw('SUM(card_orders_tax) as total_tax')
            ->selectRaw('SUM(card_orders_for_cast) as total_price_cast')
            ->whereYear('date', $date->year)
            ->whereMonth('date', $date->month)
            ->whereDay('date', '<=', 15)
            ->first();
    }

    public function getMonthlyAggregatedData($year, $month)
    {
        return $this->model
            ->selectRaw('SUM(total_orders) as total_orders')
            ->selectRaw('SUM(total_customers) as total_customers')
            ->selectRaw('SUM(total_price_cast) as total_price_cast')
            ->selectRaw('SUM(cash_orders_count) as cash_orders_count')
            ->selectRaw('SUM(cash_orders_total) as cash_orders_total')
            ->selectRaw('SUM(cash_orders_total_support_for_cast) as cash_orders_total_support_for_cast')
            ->selectRaw('SUM(cash_orders_total_unsupported_for_cast) as cash_orders_total_unsupported_for_cast')
            ->selectRaw('SUM(card_orders_count) as card_orders_count')
            ->selectRaw('SUM(card_orders_total) as card_orders_total')
            ->selectRaw('SUM(card_orders_tax) as card_orders_tax')
            ->selectRaw('SUM(card_orders_for_cast) as card_orders_for_cast')
            ->selectRaw('AVG(total_cast) as avg_total_cast')
            ->selectRaw('AVG(total_cast_working) as avg_total_cast_working')
            ->selectRaw('SUM(total_price_for_cast) as total_price_for_cast')
            ->selectRaw('SUM(dormitory_price) as dormitory_price')
            ->selectRaw('SUM(living_expenses) as living_expenses')
            ->selectRaw('SUM(decorate_room_expenses) as decorate_room_expenses')
            ->selectRaw('SUM(shop_costs) as shop_costs')
            ->selectRaw('SUM(other_expenses) as other_expenses')
            ->selectRaw('SUM(extension_orders_count) as extension_orders_count')
            ->selectRaw('SUM(total_extension_minutes) as total_extension_minutes')
            ->whereYear('date', $year)
            ->whereMonth('date', $month)
            ->first();
    }

    public function getYearlyAggregatedData($year)
    {
        return $this->model
            ->selectRaw('SUM(total_orders) as total_orders')
            ->selectRaw('SUM(total_customers) as total_customers')
            ->selectRaw('SUM(total_price_cast) as total_price_cast')
            ->selectRaw('SUM(cash_orders_count) as cash_orders_count')
            ->selectRaw('SUM(cash_orders_total) as cash_orders_total')
            ->selectRaw('SUM(cash_orders_total_support_for_cast) as cash_orders_total_support_for_cast')
            ->selectRaw('SUM(cash_orders_total_unsupported_for_cast) as cash_orders_total_unsupported_for_cast')
            ->selectRaw('SUM(card_orders_count) as card_orders_count')
            ->selectRaw('SUM(card_orders_total) as card_orders_total')
            ->selectRaw('SUM(card_orders_tax) as card_orders_tax')
            ->selectRaw('SUM(card_orders_for_cast) as card_orders_for_cast')
            ->selectRaw('AVG(total_cast) as avg_total_cast')
            ->selectRaw('AVG(total_cast_working) as avg_total_cast_working')
            ->selectRaw('SUM(total_price_for_cast) as total_price_for_cast')
            ->selectRaw('SUM(dormitory_price) as dormitory_price')
            ->selectRaw('SUM(living_expenses) as living_expenses')
            ->selectRaw('SUM(decorate_room_expenses) as decorate_room_expenses')
            ->selectRaw('SUM(shop_costs) as shop_costs')
            ->selectRaw('SUM(other_expenses) as other_expenses')
            ->selectRaw('SUM(extension_orders_count) as extension_orders_count')
            ->selectRaw('SUM(total_extension_minutes) as total_extension_minutes')
            ->whereYear('date', $year)
            ->first();
    }

    public function getDateRangeAggregatedData($startDate, $endDate)
    {
        if (!($startDate instanceof Carbon)) {
            $startDate = Carbon::parse($startDate);
        }
        if (!($endDate instanceof Carbon)) {
            $endDate = Carbon::parse($endDate);
        }

        return $this->model
            ->selectRaw('SUM(total_orders) as total_orders')
            ->selectRaw('SUM(total_customers) as total_customers')
            ->selectRaw('SUM(total_price_cast) as total_price_cast')
            ->selectRaw('SUM(cash_orders_count) as cash_orders_count')
            ->selectRaw('SUM(cash_orders_total) as cash_orders_total')
            ->selectRaw('SUM(cash_orders_total_support_for_cast) as cash_orders_total_support_for_cast')
            ->selectRaw('SUM(cash_orders_total_unsupported_for_cast) as cash_orders_total_unsupported_for_cast')
            ->selectRaw('SUM(card_orders_count) as card_orders_count')
            ->selectRaw('SUM(card_orders_total) as card_orders_total')
            ->selectRaw('SUM(card_orders_tax) as card_orders_tax')
            ->selectRaw('SUM(card_orders_for_cast) as card_orders_for_cast')
            ->selectRaw('AVG(total_cast) as avg_total_cast')
            ->selectRaw('AVG(total_cast_working) as avg_total_cast_working')
            ->selectRaw('SUM(total_price_for_cast) as total_price_for_cast')
            ->selectRaw('SUM(dormitory_price) as dormitory_price')
            ->selectRaw('SUM(living_expenses) as living_expenses')
            ->selectRaw('SUM(decorate_room_expenses) as decorate_room_expenses')
            ->selectRaw('SUM(shop_costs) as shop_costs')
            ->selectRaw('SUM(other_expenses) as other_expenses')
            ->selectRaw('SUM(extension_orders_count) as extension_orders_count')
            ->selectRaw('SUM(total_extension_minutes) as total_extension_minutes')
            ->whereDate('date', '>=', $startDate->toDateString())
            ->whereDate('date', '<=', $endDate->toDateString())
            ->first();
    }

    public function hasCompleteDataForRange($startDate, $endDate)
    {
        if (!($startDate instanceof Carbon)) {
            $startDate = Carbon::parse($startDate);
        }
        if (!($endDate instanceof Carbon)) {
            $endDate = Carbon::parse($endDate);
        }

        // Count expected days in range
        $expectedDays = $startDate->diffInDays($endDate) + 1;

        // Count actual records in range
        $actualDays = $this->model
            ->whereDate('date', '>=', $startDate->toDateString())
            ->whereDate('date', '<=', $endDate->toDateString())
            ->count();

        return $expectedDays === $actualDays;
    }

    public function getDailyDataForRange($startDate, $endDate)
    {
        if (!($startDate instanceof Carbon)) {
            $startDate = Carbon::parse($startDate);
        }
        if (!($endDate instanceof Carbon)) {
            $endDate = Carbon::parse($endDate);
        }

        return $this->model
            ->whereDate('date', '>=', $startDate->toDateString())
            ->whereDate('date', '<=', $endDate->toDateString())
            ->orderBy('date')
            ->get();
    }

    public function getMonthlyDataForYear($year)
    {
        return $this->model
            ->selectRaw('YEAR(date) as year')
            ->selectRaw('MONTH(date) as month')
            ->selectRaw('DATE_FORMAT(date, "%Y-%m") as month_key')
            ->selectRaw('SUM(total_orders) as total_orders')
            ->selectRaw('SUM(total_customers) as total_customers')
            ->selectRaw('SUM(total_price_cast) as total_price_cast')
            ->selectRaw('SUM(cash_orders_count) as cash_orders_count')
            ->selectRaw('SUM(cash_orders_total) as cash_orders_total')
            ->selectRaw('SUM(cash_orders_total_support_for_cast) as cash_orders_total_support_for_cast')
            ->selectRaw('SUM(cash_orders_total_unsupported_for_cast) as cash_orders_total_unsupported_for_cast')
            ->selectRaw('SUM(card_orders_count) as card_orders_count')
            ->selectRaw('SUM(card_orders_total) as card_orders_total')
            ->selectRaw('SUM(card_orders_tax) as card_orders_tax')
            ->selectRaw('SUM(card_orders_for_cast) as card_orders_for_cast')
            ->selectRaw('AVG(total_cast) as avg_total_cast')
            ->selectRaw('AVG(total_cast_working) as avg_total_cast_working')
            ->selectRaw('SUM(total_price_for_cast) as total_price_for_cast')
            ->selectRaw('SUM(dormitory_price) as dormitory_price')
            ->selectRaw('SUM(living_expenses) as living_expenses')
            ->selectRaw('SUM(decorate_room_expenses) as decorate_room_expenses')
            ->selectRaw('SUM(shop_costs) as shop_costs')
            ->selectRaw('SUM(other_expenses) as other_expenses')
            ->selectRaw('SUM(extension_orders_count) as extension_orders_count')
            ->selectRaw('SUM(total_extension_minutes) as total_extension_minutes')
            ->whereYear('date', $year)
            ->groupBy('year', 'month')
            ->orderBy('month')
            ->get();
    }
}
