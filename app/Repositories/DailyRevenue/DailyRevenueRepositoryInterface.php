<?php

namespace App\Repositories\DailyRevenue;

use App\Repositories\RepositoryInterface;
use Carbon\Carbon;

interface DailyRevenueRepositoryInterface extends RepositoryInterface
{
    /**
     * Get the model instance
     *
     * @return \App\Models\DailyRevenue
     */
    public function getModel();

    /**
     * Find revenue by date
     *
     * @param string|Carbon $date
     * @return \App\Models\DailyRevenue|null
     */
    public function findByDate($date);

    /**
     * Get first 15 days statistics for a given date
     *
     * @param string|Carbon $date
     * @return \App\Models\DailyRevenue|null
     */
    public function getFirst15DaysStatistics($date);

    /**
     * Get aggregated monthly revenue data
     *
     * @param int $year
     * @param int $month
     * @return object|null
     */
    public function getMonthlyAggregatedData($year, $month);

    /**
     * Get aggregated yearly revenue data
     *
     * @param int $year
     * @return object|null
     */
    public function getYearlyAggregatedData($year);

    /**
     * Get aggregated revenue data for date range
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return object|null
     */
    public function getDateRangeAggregatedData($startDate, $endDate);

    /**
     * Check if all daily revenue records exist for a date range
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return bool
     */
    public function hasCompleteDataForRange($startDate, $endDate);

    /**
     * Get daily revenue data grouped by date for a range
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getDailyDataForRange($startDate, $endDate);

    /**
     * Get daily revenue data grouped by month for a year
     *
     * @param int $year
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getMonthlyDataForYear($year);
}
