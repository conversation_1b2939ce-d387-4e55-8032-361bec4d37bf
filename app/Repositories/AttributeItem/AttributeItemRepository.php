<?php

namespace App\Repositories\AttributeItem;

use App\Models\AttributeItem;
use App\Repositories\BaseRepository;

class AttributeItemRepository extends BaseRepository implements AttributeItemRepositoryInterface
{
    public function getModel()
    {
        return AttributeItem::class;
    }

    public function createAll($listAttributeItem)
    {
        $this->model->insert($listAttributeItem);
    }

    public function deleteAll($id)
    {
        $this->model->where('attribute_id', $id)->delete();
    }
}
