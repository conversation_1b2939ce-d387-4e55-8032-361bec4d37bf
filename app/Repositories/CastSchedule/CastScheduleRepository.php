<?php

namespace App\Repositories\CastSchedule;

use App\Repositories\CastSchedule\CastScheduleRepositoryInterface;
use Carbon\Carbon;

class CastScheduleRepository implements CastScheduleRepositoryInterface
{
    const TIMEADD = 5;

    public function getNummberTable($dateStart, $dateEnd)
    {
        return ceil($dateStart->diffInMinutes($dateEnd) / 60);
    }

    public function settingOrderTime($orders = [])
    {
        $frameTimes = [];
        foreach ($orders as $index => $order) {
            $frameTimes[$index]['timeStart'] = $this->roundTimeOrder(Carbon::create($order->actual_date_start ?? $order->date_start))->format('Y-m-d H:i:s');
            $frameTimes[$index]['timeEnd'] = $this->roundTimeOrder(Carbon::create($order->actual_date_end ?? $order->date_end))->subMinutes($this::TIMEADD)->format('Y-m-d H:i:s');
            $frameTimes[$index]['colorId'] = getColorCourseById($order->course_id);
            $frameTimes[$index]['customerName'] = $order->customer_name ?? $order->name;
            $frameTimes[$index]['timeCourse'] = $order->courses->time;
        }
        return $frameTimes;
    }

    public function setTimeRest($interval, $orders = [])
    {
        $timeRests = [];
        if ($interval == 0) {
            foreach ($orders as $index => $order) {
                $timeRests[$index]['start'] = Carbon::create($order['timeEnd'])->format('Y-m-d H:i:s');
                $timeRests[$index]['middle'] = Carbon::create($order['timeEnd'])->format('Y-m-d H:i:s');
                $timeRests[$index]['end'] = Carbon::create($order['timeEnd'])->format('Y-m-d H:i:s');
            }
            return $timeRests;
        }
        if ($interval && $interval % 5) {
            $interval = $interval - $interval % 5 + 5;
        }
        foreach ($orders as $index => $order) {
            $timeRests[$index]['start'] = Carbon::create($order['timeEnd'])->addMinutes($this::TIMEADD)->format('Y-m-d H:i:s');
            $timeRests[$index]['middle'] = Carbon::create($order['timeEnd'])->addMinutes($this::TIMEADD * (ceil($interval / 10)))->format('Y-m-d H:i:s');
            $timeRests[$index]['end'] = Carbon::create($order['timeEnd'])->addMinutes($interval)->format('Y-m-d H:i:s');
        }
        return $timeRests;
    }

    function roundTimeOrder($date)
    {
        $hour = $date->hour;
        $minute = $date->minute;
        $remainder = $minute % 10;
        if ($remainder > 5) {
            $minute = $minute - $remainder + 10;
        }
        if ($remainder && $remainder < 5) {
            $minute = $minute - $remainder;
        }
        $date = $date->setTime($hour, $minute, 0);
        return $date;
    }
}
