<?php

namespace App\Repositories\Order;

use App\Constants\Cast;
use App\Repositories\RepositoryInterface;

interface OrderRepositoryInterface extends RepositoryInterface
{
     function getModel();

     /**
      * get List Orders By Date
      *
      * @param Carbon $date
      * @return collections
      */
     public function getListOrdersByDate($date);

     /**
      * getOrders
      *
      * @param  array $filters
      * @param  array $arrStatus
      * @return object
      */
     public function getListOrders($filters, $arrStatus = []);

     /**
      * getOrders
      *
      * @param  array $filters
      * @param  int $id
      * @param  array $arrStatus
      * @return object
      */
     public function getOrders($filters, $id, $arrStatus = []);

     /**
      * getTotalGuest
      *
      * @param  array $filters
      * @param  int $id
      * @param  array $arrStatus
      * @return int
      */
     public function getTotalPriceInMonth($filters, $id = NULL, $arrStatus = []);

     /**
      * getTotalPrice
      *
      * @param  int $id
      * @param  array $arrStatus
      * @return int
      */
     public function getTotalPrice($id, $date, $arrStatus = []);

     /**
      * getTimeOrder
      *
      * @param  int $castId
      * @param  datetime $dateChoose
      * @param  datetime $dateChooseEnd
      * @param  int $breakTime
      * @param  int $orderId
      * @param  array $arrMethod
      * @return object
      */
     public function getTimeOrder($castId, $dateChoose, $dateChooseEnd, $breakTime, $orderId = null, $arrMethod = []);

     /**
      *
      * @param  Carbon $date
      * @return void
      */
     public function getNumberOrder($date);

     /**
      *
      * @param  int $method
      * @param  int $isFirst
      * @return null|int
      */
     public function getTypeFee($method, $isFirst);

     /**
      *
      * @param  int $castId
      * @param  string $date
      * @return object
      */
     public function getOrderByCastId($castId, $date);

     /**
      * get list order was paid of day
      *
      * @param  string $date
      * @param  int $orderId
      * @return collection
      */
     public function getOrderPayOfDay($date, $orderId = null);

     /**
      * count order by status
      *
      * @param int $status
      * @param string $dateChoose
      * @return int
      */
     public function countOrderByStatus($status, $dateChoose = '');

     /**
      * getListOrderOfCastInDay
      *
      * @param  string $date
      * @param int $castId
      * @param boolean $isPay
      * @return object
      */
     public function getOrderOfCast($castId, $date, $isPay = false);

     /** total number of customers to date
      *  do not count duplicate customers
      * @param Carbon $date
      * @param boolean $isPay
      * @param boolean $isToday
      * @return int
      * @return int
      */
     public function getTotalCustomerPresent($date, $isPay = false, $isToday = true);

     /**
      * total number of customers to date in between time
      * do not count duplicate customers
      * @param  Carbon $dateStart
      * @param  Carbon $dateEnd
      * @return int
      */
     public function getTotalCustomerPresentByTime($dateStart, $dateEnd);

     /**
      * total price orders to date
      *
      * @param Carbon $date
      * @param  array $arrStatus
      * @param boolean $isToday
      * @return object
      */
     public function getTotalPriceOrderPresent($date, $arrStatus, $isToday = true);

     /**
      * total price orders to date in between time
      *
      * @param  Carbon $dateStart
      * @param  Carbon $dateEnd
      * @return int
      */
     public function getTotalRevenuePresentByTime($dateStart, $dateEnd);

     /**
      * get total price and count course of orders to date
      *
      * @param  Carbon $date
      * @param  array $status
      * @param boolean $isToday
      * @return collections
      */
     public function getTotalCourseCateByDateStatus($date, $status = [], $isToday = true);

     /**
      * get total price and count course of paid orders to date
      *
      * @param  mixed $date
      * @param  mixed $arrStatus
      * @param  mixed $isToday
      * @return void
      */
     public function getTotalCourseCateByDateStatusPaid($date, $arrStatus = [], $isToday = true);

     /**
      * get all orders by date
      *
      * @param  Carbon $date
      * @param boolean $isToday
      * @return collections
      */
     public function getOrderPaysByDate($date, $isToday = true);

     /**
      *
      * @param  Carbon $date
      * @param boolean $isToday
      * @return collections
      */
     public function getTotalOrderTypeFee($date, $isToday = true);

     /**
      *
      * @param  Carbon $date
      * @param boolean $isToday
      * @return collections
      */
     public function getTotalMethodGroupFee($date, $isToday = true);


     /**
      *
      * @return array
      */
     public function getLabelTypeFees();

     /**
      *
      * @param  Carbon $date
      * @param boolean $isToday
      * @return collection
      */
     public function getTotalOrderExtension($date, $isToday = true);

     /**
      *
      * @param  Carbon $dateStart
      * @param  Carbon $dateEnd
      * @param  string $groupBy
      * @return collection
      */
     public function getRevenueOrderGroupDate($dateStart, $dateEnd, $groupBy = 'date');

     /**
      *
      * @param  Carbon $dateStart
      * @param  Carbon $dateEnd
      * @param  string $groupBy
      * @return collection
      */
     public function getCourseOrderGroupDate($dateStart, $dateEnd, $groupBy = 'date');

     /**
      * set time by status Order
      *
      * @param  int $status
      * @param string $timeStart
      * @param string $timeEnd
      * @return array
      */
     public function setTimeOrder($status, $timeStart, $timeEnd);

     /**
      * get priceCourse change of Order
      *
      * @param object $order
      * @param object $courseNew
      * @param int $tax
      * @return int
      */
     public function calculatePriceCourse($order, $courseNew, $tax);

     /**
      * search order by phone
      *
      * @param  string $phone (int)
      * @return collections
      */
     public function getOrdersByPhone($phone);

     /**
      * time choose has check time plus ?
      *
      * @param  int $castId
      * @param  datetime $dateChoose
      * @param  datetime $dateChooseEnd
      * @param  int $breakTime
      * @param  int $orderId
      * @return bool
      */
     public function checkIsTimePlus($castId, $dateChoose, $dateChooseEnd, $breakTime, $orderId = null);

     /**
      * get Order By ID
      *
      * @param  int $orderID
      * @return object
      */
     public function getOrderByID($orderID);

     /**
      *
      * @param  Carbon $date
      * @return object
      */
     public function getOrderInDay($date);

     /**
      * update all order have customerId
      *
      * @param  int $customerId
      * @param  string $phone
      * @param  string $name
      * @return void
      */
     public function updateOrderByCustomerId($customerId, $phone, $name);

     /**
      * getListOrderByCastAndDate
      *
      * @param  int $castId
      * @param  Carbon $date
      * @param  array $arrMethod
      * @return object
      */
     public function getListOrderByCastAndDate($castId, $date, $arrMethod = []);

     /**
      * updatePriceSupport
      *
      * @param  int $itemId
      * @param  int $price
      * @param  int $priceChange
      * @return void
      */
     public function updatePriceSupport($itemId, $price, $priceChange);

     /**
      * totalPriceCastOrderPresentByTime
      *
      * @param  string $dateStart
      * @param  string $dateEnd
      * @return int
      */
     public function totalPriceCastOrderPresentByTime($dateStart, $dateEnd);

     /**
      * totalOrdersByCardByTime
      *
      * @param  string $dateStart
      * @param  string $dateEnd
      * @return array
      */
     public function totalOrdersByCardByTime($dateStart, $dateEnd);

     /**
      * totalOrdersByCardInDay15ByTime
      *
      * @param  Carbon $dateStart
      * @param  Carbon $dateEnd
      * @return array
      */
     public function totalOrdersByCardInDay15ByTime($dateStart, $dateEnd);

     /**
      * totalOrdersByCashByTime
      *
      * @param  string $dateStart
      * @param  string $dateEnd
      * @return array
      */
     public function totalOrdersByCashByTime($dateStart, $dateEnd);

     /**
      * listOrdersByCardInMonth
      *
      * @param  Carbon $date
      * @return object
      */
     public function listOrdersByCardInMonth($date);

     /**
      * totalPriceForCastOrderByTime
      *
      * @param  string $dateStart
      * @param  string $dateEnd
      * @return int
      */
     public function totalPriceForCastOrderByTime($dateStart, $dateEnd);

     /**
      * updateMethodByTypeFee
      *
      * @param  int $typeFee
      * @return int
      */
     public function updateMethodByTypeFee($typeFee);

     /**
      * update IsFirst By TypeFee
      *
      * @param  int $typeFee
      * @return int
      */
     public function updateIsFirstByTypeFee($typeFee);
}
