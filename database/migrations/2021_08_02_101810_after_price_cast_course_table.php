<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AfterPriceCastCourseTable extends Migration
{
    private $table = 'courses';
    private $col1 = 'price_cast_no_first';
    private $col2 = 'price_cast_free';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn($this->table, $this->col1)) {
            Schema::table($this->table, function (Blueprint $table) {
                $table->integer($this->col1)->default(0);
                $table->integer($this->col2)->default(0);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn($this->table, $this->col1)) {
            Schema::table($this->table, function (Blueprint $table) {
                $table->dropColumn($this->col1);
                $table->dropColumn($this->col2);
            });
        }
    }
}
