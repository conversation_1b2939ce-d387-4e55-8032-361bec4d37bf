<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AfterKeyPageUserRolesTable extends Migration
{
    private $table = 'user_roles';
    private $col = 'key_page';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn($this->table, $this->col)) {
            Schema::table($this->table, function (Blueprint $table) {
                $table->string($this->col, 100)->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn($this->table, $this->col)) {
            Schema::table($this->table, function (Blueprint $table) {
                $table->dropColumn($this->col);
            });
        }
    }
}
