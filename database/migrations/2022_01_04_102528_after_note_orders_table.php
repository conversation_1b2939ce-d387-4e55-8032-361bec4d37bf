<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AfterNoteOrdersTable extends Migration
{
    private $table = 'orders';
    private $col = 'note';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn($this->table, $this->col)) {
            Schema::table($this->table, function (Blueprint $table) {
                $table->string($this->col, 1000);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn($this->table, $this->col)) {
            Schema::table($this->table, function (Blueprint $table) {
                $table->dropColumn($this->col);
            });
        }
    }
}
