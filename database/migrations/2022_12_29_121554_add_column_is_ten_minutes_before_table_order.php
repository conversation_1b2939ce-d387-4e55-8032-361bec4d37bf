<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnIsTenMinutesBeforeTableOrder extends Migration
{
    private $table = 'orders';
    private $col = 'is_ten_minutes_before';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn($this->table, $this->col)) {
            Schema::table($this->table, function (Blueprint $table) {
                $table->tinyInteger($this->col)->after('is_minute_add')->default(0);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn($this->table, $this->col)) {
            Schema::table($this->table, function (Blueprint $table) {
                $table->dropColumn($this->col);
            });
        }
    }
}
