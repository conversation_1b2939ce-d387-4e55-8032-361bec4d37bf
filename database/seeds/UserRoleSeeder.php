<?php

use App\Models\UserRole;
use App\User;;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Route;

class UserRoleSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $user = User::where("email", 'admin')->first();

        $routeCollection = Route::getRoutes();
        $rows = [];
        foreach ($routeCollection as $value) {
            if (strpos($value->getName(), "admin.") === false) continue;
            foreach ($value->methods() as $method) {
                if ($method != 'GET') continue;
                echo $value->getName() . '--' . join('-', $value->methods()) . "\n";
                $rows[] = [
                    "user_id" => $user->id,
                    "page" => $value->getName(),
                    "type" => 2,
                ];
            }
        }
        foreach ($rows as $item) {
            UserRole::updateOrCreate($item);
        }
    }
}
