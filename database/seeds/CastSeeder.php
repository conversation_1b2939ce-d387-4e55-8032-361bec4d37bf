<?php

use App\Models\Attribute;
use App\Models\Cast;
use App\Models\CastAttribute;
use App\Models\CastCalendar;
use App\Models\Coupon;
use App\Models\Option;
use Faker\Generator as Faker;
use Illuminate\Database\Seeder;
use Illuminate\Support\Carbon;

class CastSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(Faker $faker)
    {
        $arrImage = [
            'images/casts/cast1.jpg',
            'images/casts/cast2.jpg',
            'images/casts/cast3.jpg',
        ];
        $casts = factory(Cast::class, 15)->create();

        foreach ($casts as $cast) {
            $cast->images()->create([
                'cast_id' => $cast->id,
                'path' => $arrImage[array_rand($arrImage)],
            ]);
            $cast->coupons()->sync(Coupon::inRandomOrder()->limit(mt_rand(3, 5))->get()->pluck('id')->toArray());
            $cast->options()->sync(Option::whereNull('parent')->inRandomOrder()->limit(mt_rand(3, 5))->get()->pluck('id')->toArray());

            $this->insertCastCalendar($cast->id, $faker);
            $attribute = Attribute::inRandomOrder()->first();
            if ($attribute->type == 5 || $attribute->type == 6) {
                $castAttribute = [
                    'cast_id' => $cast->id,
                    'attribute_id' => $attribute->id,
                    'answer' => $faker->text(100),
                ];
            } else {
                $castAttribute = [
                    'cast_id' => $cast->id,
                    'attribute_id' => $attribute->id,
                    'answer' => trim(implode(',', $attribute->attributeItem()->limit(mt_rand(1, 2))->get()->pluck('id')->toArray()), ','),
                ];
            }
            CastAttribute::create($castAttribute);
        }
    }

    public function insertCastCalendar($idCast, Faker $faker)
    {
        for ($i = 0; $i < 3; $i++) {
            if ($faker->numberBetween(0, 1)) continue;
            $dateStart = $faker->dateTimeBetween('now', '+6 day');
            $dateStart->setTime($dateStart->format('H'), $dateStart->format('i'), 0);
            $dateStartClone = clone $dateStart;
            $castCalendars = CastCalendar::where('cast_id', '=', $idCast)->get();
            if (Carbon::parse($dateStartClone->format('Y-m-d 08:00:00')) > $dateStart) {
                $dateStart = Carbon::parse($dateStartClone->format('Y-m-d 08:00'));
            }
            $check = true;
            foreach ($castCalendars as $item) {
                if ($item->cast_id == $idCast && Carbon::parse($item->date_start)->format('Y-m-d') == $dateStart->format('Y-m-d'))
                    $check = false;
            }
            if (!$check) continue;
            $type = $faker->numberBetween(1, 2);
            $dateEnd =  Carbon::parse($dateStartClone->format('Y-m-d 23:00:00'));
            $date = clone $dateStart;
            if ($type == 2) {
                $dateStart = null;
                $dateEnd = null;
            }
            factory(CastCalendar::class)->create([
                'cast_id' => $idCast,
                'date' => $date,
                'date_start' => $dateStart,
                'date_end' => $dateEnd,
                'price_cast' => Cast::find($idCast)->dormitory_price * -1,
                'type' => $type,
                'is_room_change' => $type == 2 ? null : $faker->numberBetween(0, 1),
                'created_by' => 1,
            ]);
        }
        return;
    }
}
