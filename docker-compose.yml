version: '3'
services:
  # frontend:
  #   build:
  #     context: .
  #     dockerfile: docker/Nodejs.Dockerfile
  #   volumes:
  #     - ./:/usr/src/app
  #     - ./docker/:/docker/
  #   # ports:
  #   #  - 3000:3000
  #   environment:
  #     NODE_ENV: development
  #   command: sh ./docker/nodejs.sh
  #   networks:
  #     - app-network
  php72:
    build:
      context: .
      dockerfile: docker/Php72.Dockerfile
    image: php72
    # container_name: php72
    restart: unless-stopped
    tty: true
    environment:
      SERVICE_NAME: php72
      SERVICE_TAGS: dev
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - app-network
  webserver:
    image: nginx:alpine
    # container_name: webserver
    restart: unless-stopped
    tty: true
    ports:
      - 8081:80
      # - "443:443"
    volumes:
      - ./:/var/www
      - ./docker/nginx/app.d/:/etc/nginx/conf.d/
    networks:
      - app-network
  #Scheduler
  scheduler:
    image: php72
    command: /var/www/docker/work.sh schedule
    # container_name: scheduler_laravel_api
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./docker/:/var/www/docker
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - app-network
  #Queue
  queue:
    image: php72
    command: /var/www/docker/work.sh queue
    # container_name: queue_laravel_api
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./docker/:/var/www/docker
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - app-network
  redis:
    image: redis:alpine
    # container_name: myapp-redis
    command: redis-server --appendonly yes --requirepass "${REDIS_PASSWORD}"
    volumes:
      - ./data/redis:/redist-data
      - ./docker/redis/sysctl.conf:/etc/sysctl.conf
    ports:
      - "8002:6379"
    networks:
      - app-network
  adminer:
    image: adminer
    restart: always
    ports:
      - ${ADMINER_PORT}:8080
    links:
      - db
    depends_on:
      - db
    networks:
      - app-network
  #MySQL Service
  db:
    image: mysql:5.7.22
    # container_name: db
    restart: unless-stopped
    tty: true
    ports:
     - "33060:3306"
    environment:
      MYSQL_DATABASE: db
      MYSQL_USERNAME: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      SERVICE_TAGS: dev
      SERVICE_NAME: mysql
    volumes:
      - dbdata:/var/lib/mysql/
      - ./docker/mysql/my.cnf:/etc/mysql/my.cnf
    networks:
      - app-network
#Docker Networks
networks:
  app-network:
    driver: bridge
#Volumes
volumes:
  dbdata:
    driver: local
